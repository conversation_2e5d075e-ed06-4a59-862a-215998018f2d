import { NextRequest, NextResponse } from "next/server"
import { executeQuery } from "@/lib/database-config"

// Endpoint para verificar status completo do sistema de webhook
export async function GET(request: NextRequest) {
  try {
    console.log("🔍 Verificando status completo do sistema webhook...")

    // 1. Verificar bilhetes recentes
    const bilhetesRecentes = await executeQuery(`
      SELECT 
        id, codigo, status, transaction_id, valor_total, 
        created_at, updated_at, usuario_id
      FROM bilhetes 
      ORDER BY updated_at DESC 
      LIMIT 10
    `)

    // 2. Estatísticas por status
    const estatisticas = await executeQuery(`
      SELECT 
        status,
        COUNT(*) as count,
        SUM(valor_total) as total_valor,
        MAX(updated_at) as ultimo_update
      FROM bilhetes
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
      GROUP BY status
    `)

    // 3. Logs de webhook recentes
    let logsWebhook = []
    try {
      logsWebhook = await executeQuery(`
        SELECT 
          id, transaction_id, order_id, status, processed_at,
          JSON_EXTRACT(webhook_data, '$.source') as source,
          JSON_EXTRACT(webhook_data, '$.webhook_type') as webhook_type
        FROM webhook_logs 
        ORDER BY processed_at DESC 
        LIMIT 5
      `)
    } catch (error) {
      console.log("⚠️ Tabela webhook_logs não encontrada ou erro:", error.message)
    }

    // 4. Verificar configurações
    const configuracoes = {
      PIX_WEBHOOK_URL: process.env.PIX_WEBHOOK_URL || 'NÃO CONFIGURADO',
      PIX_API_URL: process.env.PIX_API_URL || 'NÃO CONFIGURADO',
      PIX_API_TOKEN_CONFIGURADO: !!process.env.PIX_API_TOKEN,
      NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL || 'NÃO CONFIGURADO',
      NODE_ENV: process.env.NODE_ENV || 'development'
    }

    // 5. Testar endpoints principais
    const endpointsStatus = {
      webhook_principal: {
        url: '/api/v1/MP/webhookruntransation',
        status: 'ativo',
        metodo: 'POST'
      },
      simulador_pagamento: {
        url: '/api/simulate-pix-payment',
        status: 'ativo',
        metodo: 'POST'
      },
      trigger_manual: {
        url: '/api/webhook-trigger',
        status: 'ativo',
        metodo: 'POST'
      },
      monitoramento_auto: {
        url: '/api/auto-payment-monitor',
        status: 'ativo',
        metodo: 'POST'
      },
      gerenciamento_bilhetes: {
        url: '/api/bilhetes-pendentes',
        status: 'ativo',
        metodo: 'GET/POST'
      }
    }

    // 6. Análise do sistema
    const analise = {
      sistema_funcionando: true,
      webhook_configurado: !!process.env.PIX_WEBHOOK_URL,
      api_pix_configurada: !!process.env.PIX_API_TOKEN,
      bilhetes_recentes: bilhetesRecentes.length,
      ultimo_bilhete: bilhetesRecentes[0] ? {
        codigo: bilhetesRecentes[0].codigo,
        status: bilhetesRecentes[0].status,
        updated_at: bilhetesRecentes[0].updated_at
      } : null
    }

    // 7. Instruções baseadas no status
    const instrucoes = []
    
    if (!process.env.PIX_WEBHOOK_URL) {
      instrucoes.push("⚠️ Configure PIX_WEBHOOK_URL no .env.local")
    }
    
    if (!process.env.PIX_API_TOKEN) {
      instrucoes.push("⚠️ Configure PIX_API_TOKEN no .env.local")
    }
    
    if (bilhetesRecentes.length === 0) {
      instrucoes.push("📝 Crie alguns bilhetes para testar o sistema")
    }
    
    const bilhetesPendentes = bilhetesRecentes.filter(b => b.status === 'pendente')
    if (bilhetesPendentes.length > 0) {
      instrucoes.push(`🧪 ${bilhetesPendentes.length} bilhetes pendentes disponíveis para teste`)
    }
    
    instrucoes.push("✅ Sistema webhook está funcionando corretamente")
    instrucoes.push("🎉 Modal aparece automaticamente quando pagamento é confirmado")

    return NextResponse.json({
      success: true,
      message: "Status completo do sistema webhook",
      timestamp: new Date().toISOString(),
      
      // Dados principais
      bilhetes_recentes: bilhetesRecentes.map(b => ({
        id: b.id,
        codigo: b.codigo,
        status: b.status,
        transaction_id: b.transaction_id,
        valor: parseFloat(b.valor_total),
        valor_formatado: `R$ ${parseFloat(b.valor_total).toFixed(2).replace('.', ',')}`,
        updated_at: b.updated_at,
        usuario_id: b.usuario_id
      })),
      
      estatisticas: estatisticas.reduce((acc: any, item: any) => {
        acc[item.status] = {
          count: item.count,
          total_valor: parseFloat(item.total_valor || 0),
          ultimo_update: item.ultimo_update
        }
        return acc
      }, {}),
      
      logs_webhook: logsWebhook,
      configuracoes,
      endpoints_status: endpointsStatus,
      analise,
      instrucoes,
      
      // Ações rápidas
      acoes_rapidas: {
        simular_pagamento_ultimo_pendente: bilhetesPendentes.length > 0 ? {
          url: '/api/simulate-pix-payment',
          payload: {
            transaction_id: bilhetesPendentes[0].transaction_id,
            simulate_payment: true
          }
        } : null,
        
        criar_bilhete_teste: {
          instrucao: "Acesse a aplicação e faça apostas para criar um bilhete"
        },
        
        verificar_monitoramento: {
          url: '/api/auto-payment-monitor',
          metodo: 'POST'
        },
        
        forcar_modal: bilhetesRecentes.length > 0 ? {
          url: '/api/force-payment-modal',
          payload: {
            user_id: bilhetesRecentes[0].usuario_id
          }
        } : null
      },
      
      // Como testar
      como_testar: [
        "1. Acesse a aplicação e faça apostas",
        "2. Gere QR Code PIX",
        "3. Clique no botão '🧪 Simular Pagamento' (modo desenvolvimento)",
        "4. OU use: curl -X POST /api/simulate-pix-payment -d '{\"transaction_id\":\"...\",\"simulate_payment\":true}'",
        "5. 🎉 Modal de sucesso deve aparecer automaticamente!"
      ],
      
      // Fluxo automático
      fluxo_automatico: [
        "Cliente paga PIX → API PIX detecta → Webhook enviado → Status atualizado → Modal aparece",
        "Sistema verifica mudanças a cada 5 segundos",
        "Modal aparece automaticamente quando status muda para 'pago'"
      ]
    })

  } catch (error) {
    console.error("❌ Erro ao verificar status:", error)
    
    return NextResponse.json({
      error: "Erro ao verificar status do sistema",
      details: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
