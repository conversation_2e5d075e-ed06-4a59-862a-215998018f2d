#!/usr/bin/env node

// Script para testar o webhook PIX
import https from 'https';

const WEBHOOK_URL = 'https://ouroemu.site/api/v1/MP/webhookruntransation';

// Função para fazer requisição POST
function makeRequest(data) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(data);
    
    const options = {
      hostname: 'ouroemu.site',
      port: 443,
      path: '/api/v1/MP/webhookruntransation',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = https.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonResponse = JSON.parse(responseData);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: jsonResponse
          });
        } catch (e) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: responseData
          });
        }
      });
    });

    req.on('error', (e) => {
      reject(e);
    });

    req.write(postData);
    req.end();
  });
}

// Função principal de teste
async function testWebhook() {
  console.log('🧪 Testando Webhook PIX...\n');

  // Teste 1: Webhook com pagamento aprovado (bilhete real)
  console.log('📋 Teste 1: Pagamento Aprovado - Bilhete Real');
  try {
    const response1 = await makeRequest({
      order_id: "BLT175312542333427L1NORV", // Bilhete real criado
      status: "PAID",
      type: "PIXOUT",
      message: "Payment approved"
    });
    
    console.log('✅ Status:', response1.statusCode);
    console.log('📄 Resposta:', JSON.stringify(response1.body, null, 2));
  } catch (error) {
    console.log('❌ Erro:', error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Teste 2: Webhook com order_id genérico
  console.log('📋 Teste 2: Order ID Genérico');
  try {
    const response2 = await makeRequest({
      order_id: "CODIGO_DO_BILHETE",
      status: "PAID",
      type: "PIXOUT",
      message: "Payment approved"
    });
    
    console.log('✅ Status:', response2.statusCode);
    console.log('📄 Resposta:', JSON.stringify(response2.body, null, 2));
  } catch (error) {
    console.log('❌ Erro:', error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Teste 3: Consultar estatísticas (GET)
  console.log('📋 Teste 3: Consultar Estatísticas');
  try {
    const getOptions = {
      hostname: 'ouroemu.site',
      port: 443,
      path: '/api/v1/MP/webhookruntransation',
      method: 'GET'
    };

    const getResponse = await new Promise((resolve, reject) => {
      const req = https.request(getOptions, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          try {
            resolve({
              statusCode: res.statusCode,
              body: JSON.parse(data)
            });
          } catch (e) {
            resolve({
              statusCode: res.statusCode,
              body: data
            });
          }
        });
      });
      req.on('error', reject);
      req.end();
    });
    
    console.log('✅ Status:', getResponse.statusCode);
    console.log('📄 Resposta:', JSON.stringify(getResponse.body, null, 2));
  } catch (error) {
    console.log('❌ Erro:', error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Teste 4: Webhook localhost
  console.log('📋 Teste 4: Webhook Localhost');
  try {
    const response4 = await makeRequest({
      order_id: "BLT175312542333427L1NORV",
      status: "PAID",
      type: "PIXOUT",
      message: "Payment approved"
    });

    console.log('✅ Status:', response4.statusCode);
    console.log('📄 Resposta:', JSON.stringify(response4.body, null, 2));
  } catch (error) {
    console.log('❌ Erro:', error.message);
  }

  console.log('\n🎯 Teste concluído!');
}

// Função para testar localhost
async function testLocalhost() {
  console.log('🧪 Testando Webhook PIX Localhost...\n');

  try {
    const response = await fetch('http://localhost:3000/api/v1/MP/webhookruntransation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        order_id: "BLT175312542333427L1NORV",
        status: "PAID",
        type: "PIXOUT",
        message: "Payment approved"
      })
    });

    const result = await response.json();
    console.log('✅ Status:', response.status);
    console.log('📄 Resposta:', JSON.stringify(result, null, 2));
  } catch (error) {
    console.log('❌ Erro:', error.message);
  }
}

// Executar teste
if (process.argv.includes('--localhost')) {
  testLocalhost().catch(console.error);
} else {
  testWebhook().catch(console.error);
}
