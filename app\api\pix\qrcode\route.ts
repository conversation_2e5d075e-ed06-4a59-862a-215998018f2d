import { NextRequest, NextResponse } from 'next/server'
import QRCode from 'qrcode'

const PIX_API_BASE_URL = process.env.PIX_API_URL || 'https://joanadeoxum.com/api/v1'
const PIX_TOKEN = process.env.PIX_API_TOKEN || 'Gk0e0W5bc2Guozw97NXv8duZbZCDbsedwMuTQNqqn5ZMT549Z0qDDZBF5ZbG9sHhXw9egbly3eiFJ4PKfs6ur+zzXs30gSDV6CYl8sQmSPJaISqpqf/FtJ2k30O3kO9ZZsNwasalD0PGpD2wlAbb4ynO5S07P1kgWZRtw4w=='
const PIX_WEBHOOK_URL = process.env.PIX_WEBHOOK_URL || 'https://seudominio.com/api/v1/MP/webhookruntransation'

export async function POST(request: NextRequest) {
  try {
    let body
    try {
      body = await request.json()
    } catch (parseError) {
      console.error('❌ Erro ao parsear JSON da requisição:', parseError)
      return NextResponse.json(
        { error: 'JSON inválido na requisição' },
        { status: 400 }
      )
    }
    
    const {
      value,
      description = 'Pagamento de aposta',
      client_name,
      client_email,
      client_document,
      qrcode_image = false
    } = body

    // Validações
    if (!value || value <= 0) {
      return NextResponse.json(
        { error: 'Valor deve ser maior que zero' },
        { status: 400 }
      )
    }

    if (!client_name || !client_email || !client_document) {
      return NextResponse.json(
        { error: 'Nome, email e documento do cliente são obrigatórios' },
        { status: 400 }
      )
    }

    // Limpar formatação do CPF (remover pontos e traços)
    const cleanCpf = client_document.replace(/[.-]/g, '')
    console.log('🧹 CPF limpo:', { original: client_document, clean: cleanCpf })

    // Preparar dados para a API do PIX
    const pixData = {
      token: PIX_TOKEN,
      value: parseFloat(value.toString()),
      description,
      client_name,
      client_email,
      client_document: cleanCpf, // Usar CPF limpo
      qrcode_image,
      webhook_url: PIX_WEBHOOK_URL // URL do webhook para notificações
    }

    console.log('🔄 Solicitando QR Code PIX:', {
      value: pixData.value,
      client_name: pixData.client_name,
      client_email: pixData.client_email
    })

    // Fazer requisição para a API do PIX com timeout personalizado
    console.log('🔄 Fazendo requisição para API PIX real...')

    let pixResponse

    try {
      // Criar AbortController para timeout personalizado
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 15000) // 15 segundos

      const response = await fetch(`${PIX_API_BASE_URL}/Transacao/SolicitacaoQRCode`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(pixData),
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('❌ Erro na API PIX:', response.status, errorText)
        return NextResponse.json(
          {
            error: `Erro na API PIX: ${response.status}`,
            details: errorText,
            message: 'Não foi possível gerar o QR Code PIX. Tente novamente.'
          },
          { status: response.status }
        )
      }

      const responseText = await response.text()
      console.log('📥 Resposta bruta da API PIX:', responseText.substring(0, 200) + '...')

      try {
        pixResponse = JSON.parse(responseText)
      } catch (parseError) {
        console.error('❌ Erro ao parsear resposta da API PIX:', parseError)
        return NextResponse.json(
          {
            error: 'Resposta inválida da API PIX',
            details: responseText.substring(0, 500),
            message: 'Erro interno na API PIX. Contate o suporte.'
          },
          { status: 500 }
        )
      }

      // Verificar se a resposta contém os dados necessários
      if (!pixResponse || !pixResponse.qr_code_value || !pixResponse.transaction_id) {
        console.error('❌ Resposta da API PIX incompleta:', pixResponse)
        return NextResponse.json(
          {
            error: 'Dados incompletos da API PIX',
            details: pixResponse,
            message: 'A API PIX não retornou os dados necessários.'
          },
          { status: 500 }
        )
      }

      console.log('✅ QR Code PIX gerado pela API real:', {
        transaction_id: pixResponse.transaction_id,
        order_id: pixResponse.order_id,
        status: pixResponse.status,
        has_qr_code: !!pixResponse.qr_code_value,
        has_image: !!pixResponse.qrcode_image,
        qr_code_length: pixResponse.qr_code_value?.length
      })

    } catch (fetchError) {
      console.error('❌ Erro de conexão com API PIX:', fetchError)

      // Se for ambiente de desenvolvimento, usar fallback
      if (process.env.NODE_ENV === 'development') {
        console.log('🔄 Usando fallback para desenvolvimento...')

        // Gerar dados simulados para desenvolvimento
        const mockTransactionId = `DEV_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        const mockPixCode = `00020126580014br.gov.bcb.pix0136${mockTransactionId}520400005303986540${value.toFixed(2)}5802BR5913${client_name.substring(0, 25)}6009SAO PAULO62070503***6304`

        pixResponse = {
          success: true,
          transaction_id: mockTransactionId,
          order_id: `ORDER_${mockTransactionId}`,
          qr_code_value: mockPixCode,
          status: 'pending',
          expiration_datetime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24h
          qrcode_image: null
        }

        console.log('✅ Dados PIX simulados gerados para desenvolvimento:', {
          transaction_id: pixResponse.transaction_id,
          has_qr_code: !!pixResponse.qr_code_value
        })
      } else {
        // Em produção, retornar erro
        return NextResponse.json(
          {
            error: 'Erro de conexão com API PIX',
            details: fetchError.message,
            message: 'Não foi possível conectar com a API PIX. Verifique sua conexão ou tente novamente mais tarde.'
          },
          { status: 500 }
        )
      }
    }

    // Verificar se pixResponse foi definido
    if (!pixResponse) {
      console.error('❌ pixResponse não foi definido')
      return NextResponse.json(
        {
          error: 'Erro interno',
          message: 'Falha na comunicação com a API PIX.'
        },
        { status: 500 }
      )
    }

    console.log('✅ QR Code PIX gerado com sucesso:', {
      transaction_id: pixResponse.transaction_id,
      order_id: pixResponse.order_id,
      status: pixResponse.status
    })

    // Validar e corrigir formato do QR Code se necessário
    if (pixResponse.qr_code_value) {
      console.log('🔍 Validando formato PIX:', {
        length: pixResponse.qr_code_value.length,
        startsCorrect: pixResponse.qr_code_value.startsWith('00020126'),
        containsPix: pixResponse.qr_code_value.includes('br.gov.bcb.pix'),
        containsCurrency: pixResponse.qr_code_value.includes('5303986'),
        containsCountry: pixResponse.qr_code_value.includes('5802BR')
      })
    }

    // Gerar QR Code visual se não veio da API
    let qrCodeImage = pixResponse.qrcode_image
    if (!qrCodeImage && pixResponse.qr_code_value) {
      try {
        qrCodeImage = await QRCode.toDataURL(pixResponse.qr_code_value, {
          width: 300,
          margin: 2,
          color: {
            dark: '#000000',
            light: '#FFFFFF'
          },
          errorCorrectionLevel: 'M'
        })
        console.log('✅ QR Code visual gerado localmente')
      } catch (qrError) {
        console.error('❌ Erro ao gerar QR Code visual:', qrError)
      }
    }

    // Retornar resposta formatada
    return NextResponse.json({
      success: true,
      data: {
        qr_code_value: pixResponse.qr_code_value,
        qrcode_image: qrCodeImage,
        expiration_datetime: pixResponse.expiration_datetime,
        status: pixResponse.status,
        transaction_id: pixResponse.transaction_id,
        order_id: pixResponse.order_id,
        value: pixData.value,
        client_name: pixData.client_name,
        client_email: pixData.client_email
      }
    })

  } catch (error) {
    console.error('❌ Erro interno ao gerar QR Code PIX:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
