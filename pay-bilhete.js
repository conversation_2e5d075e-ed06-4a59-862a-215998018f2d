#!/usr/bin/env node

// Comando simples para pagar bilhetes
import fetch from 'node-fetch';

async function payBilhete(codigo) {
  try {
    console.log(`💳 Processando pagamento para: ${codigo}`);
    
    const response = await fetch(`http://localhost:3000/api/smart-webhook?order_id=${codigo}`, {
      method: 'GET'
    });
    
    const result = await response.json();
    
    if (result.success) {
      console.log(`✅ PAGO! Método: ${result.method}`);
      if (result.fallback_reason) {
        console.log(`⚠️ Fallback usado: ${result.fallback_reason}`);
      }
      return true;
    } else {
      console.log(`❌ ERRO: ${result.message}`);
      if (result.errors) {
        console.log(`   Remoto: ${result.errors.remote}`);
        console.log(`   Local: ${result.errors.local}`);
      }
      return false;
    }
  } catch (error) {
    console.error(`❌ Erro: ${error.message}`);
    return false;
  }
}

async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('💳 Pagador de Bilhetes');
    console.log('📝 Uso: node pay-bilhete.js <CODIGO_BILHETE>');
    console.log('📝 Exemplo: node pay-bilhete.js BLT175312747595627AI4WKL');
    return;
  }
  
  const codigo = args[0];
  const success = await payBilhete(codigo);
  
  if (success) {
    console.log('🎉 Pagamento processado com sucesso!');
  } else {
    console.log('💔 Falha no processamento do pagamento');
    process.exit(1);
  }
}

main().catch(console.error);
