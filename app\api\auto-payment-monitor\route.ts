import { NextRequest, NextResponse } from "next/server"
import { executeQuery } from "@/lib/database-config"

// Sistema de Monitoramento Automático de Pagamentos PIX
export async function POST(request: NextRequest) {
  try {
    console.log("🔍 Iniciando monitoramento automático de pagamentos...")

    // Buscar bilhetes pendentes dos últimos 30 minutos
    const bilhetesPendentes = await executeQuery(`
      SELECT 
        id,
        codigo,
        transaction_id,
        pix_order_id,
        valor_total,
        created_at,
        usuario_id,
        status
      FROM bilhetes 
      WHERE status = 'pendente' 
      AND created_at >= DATE_SUB(NOW(), INTERVAL 30 MINUTE)
      ORDER BY created_at DESC
      LIMIT 20
    `)

    console.log(`📊 Encontrados ${bilhetesPendentes.length} bilhetes pendentes para verificar`)

    if (bilhetesPendentes.length === 0) {
      return NextResponse.json({
        success: true,
        message: "Nenhum bilhete pendente encontrado",
        checked: 0,
        processed: 0,
        timestamp: new Date().toISOString()
      })
    }

    let bilhetesProcessados = 0
    const resultados = []

    // Verificar cada bilhete pendente
    for (const bilhete of bilhetesPendentes) {
      try {
        console.log(`🔍 Verificando bilhete: ${bilhete.codigo}`)

        // Simular verificação de pagamento na API PIX
        // Em um cenário real, você faria uma consulta à API meiodepagamento.com
        const statusPagamento = await verificarStatusPagamento(bilhete.transaction_id)

        if (statusPagamento.pago) {
          console.log(`💰 Pagamento confirmado para bilhete: ${bilhete.codigo}`)

          // Disparar webhook automaticamente
          const webhookResult = await dispararWebhookAutomatico(bilhete, request.nextUrl.origin)

          if (webhookResult.success) {
            bilhetesProcessados++
            resultados.push({
              bilhete_codigo: bilhete.codigo,
              transaction_id: bilhete.transaction_id,
              status: 'webhook_disparado',
              webhook_response: webhookResult
            })
          } else {
            resultados.push({
              bilhete_codigo: bilhete.codigo,
              transaction_id: bilhete.transaction_id,
              status: 'erro_webhook',
              error: webhookResult.error
            })
          }
        } else {
          resultados.push({
            bilhete_codigo: bilhete.codigo,
            transaction_id: bilhete.transaction_id,
            status: 'ainda_pendente',
            verificado_em: new Date().toISOString()
          })
        }

      } catch (error) {
        console.error(`❌ Erro ao verificar bilhete ${bilhete.codigo}:`, error)
        resultados.push({
          bilhete_codigo: bilhete.codigo,
          transaction_id: bilhete.transaction_id,
          status: 'erro_verificacao',
          error: error instanceof Error ? error.message : String(error)
        })
      }
    }

    return NextResponse.json({
      success: true,
      message: "Monitoramento automático concluído",
      timestamp: new Date().toISOString(),
      estatisticas: {
        bilhetes_verificados: bilhetesPendentes.length,
        pagamentos_detectados: bilhetesProcessados,
        webhooks_disparados: bilhetesProcessados
      },
      resultados
    })

  } catch (error) {
    console.error("❌ Erro no monitoramento automático:", error)
    
    return NextResponse.json({
      error: "Erro no monitoramento automático",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}

// Função para verificar status do pagamento na API PIX
async function verificarStatusPagamento(transactionId: string) {
  try {
    // Aqui você faria uma consulta real à API meiodepagamento.com
    // Por enquanto, vamos simular baseado em alguns critérios
    
    const PIX_API_URL = process.env.PIX_API_URL || 'https://api.meiodepagamento.com/api/V1'
    const PIX_TOKEN = process.env.PIX_API_TOKEN

    if (!PIX_TOKEN) {
      console.log("⚠️ Token PIX não configurado, simulando verificação")
      return { pago: false, simulado: true }
    }

    // Tentar consultar a API real
    try {
      const response = await fetch(`${PIX_API_URL}/ConsultarTransacao`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: PIX_TOKEN,
          transaction_id: transactionId
        }),
        signal: AbortSignal.timeout(10000) // 10 segundos timeout
      })

      if (response.ok) {
        const data = await response.json()
        console.log(`📋 Status da API PIX para ${transactionId}:`, data)
        
        // Verificar se o status indica pagamento confirmado
        const statusPago = data.status === 'PAID' || 
                          data.status === 'paid' || 
                          data.status === 'APROVADO' ||
                          data.status === 'aprovado' ||
                          data.payment_status === 'PAID'

        return { 
          pago: statusPago, 
          status_original: data.status,
          data_resposta: data 
        }
      } else {
        console.log(`⚠️ API PIX retornou erro ${response.status} para ${transactionId}`)
        return { pago: false, erro_api: response.status }
      }

    } catch (apiError) {
      console.log(`⚠️ Erro ao consultar API PIX para ${transactionId}:`, apiError)
      return { pago: false, erro_consulta: true }
    }

  } catch (error) {
    console.error("❌ Erro na verificação de pagamento:", error)
    return { pago: false, erro: true }
  }
}

// Função para disparar webhook automaticamente
async function dispararWebhookAutomatico(bilhete: any, baseUrl: string) {
  try {
    const webhookPayload = {
      order_id: bilhete.transaction_id || bilhete.codigo,
      transaction_id: bilhete.transaction_id,
      qr_code_payment_id: bilhete.transaction_id,
      status: "PAID",
      type: "PIXOUT",
      message: "Pagamento detectado automaticamente",
      amount: parseFloat(bilhete.valor_total),
      description: `Bolão - ${bilhete.codigo}`,
      end_to_end_id: `E${Date.now()}`,
      last_updated_at: new Date().toISOString(),
      source: "auto_monitor"
    }

    const webhookUrl = `${baseUrl}/api/v1/MP/webhookruntransation`
    
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Sistema-Bolao-Auto-Monitor/1.0'
      },
      body: JSON.stringify(webhookPayload),
      signal: AbortSignal.timeout(15000) // 15 segundos timeout
    })

    const result = await response.json()

    return {
      success: response.ok,
      status: response.status,
      payload: webhookPayload,
      response: result
    }

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    }
  }
}

// Método GET para status do monitoramento
export async function GET(request: NextRequest) {
  try {
    // Buscar estatísticas dos últimos bilhetes
    const stats = await executeQuery(`
      SELECT 
        status,
        COUNT(*) as count,
        SUM(valor_total) as total_valor
      FROM bilhetes
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL 2 HOUR)
      GROUP BY status
    `)

    return NextResponse.json({
      message: "Sistema de Monitoramento Automático de Pagamentos",
      status: "ativo",
      timestamp: new Date().toISOString(),
      estatisticas: stats.reduce((acc: any, item: any) => {
        acc[item.status] = {
          count: item.count,
          total_valor: parseFloat(item.total_valor || 0)
        }
        return acc
      }, {}),
      configuracao: {
        intervalo_verificacao: "Manual via POST",
        tempo_limite: "30 minutos",
        bilhetes_por_execucao: 20,
        timeout_api: "10 segundos",
        timeout_webhook: "15 segundos"
      },
      endpoints: {
        executar_monitoramento: "POST /api/auto-payment-monitor",
        status: "GET /api/auto-payment-monitor",
        disparar_webhook_manual: "POST /api/webhook-trigger"
      },
      instrucoes: [
        "1. Execute POST /api/auto-payment-monitor para verificar pagamentos",
        "2. O sistema consulta a API PIX para cada bilhete pendente",
        "3. Pagamentos confirmados disparam webhook automaticamente",
        "4. Use POST /api/webhook-trigger para disparar webhook manualmente"
      ]
    })

  } catch (error) {
    return NextResponse.json({
      error: "Erro ao buscar estatísticas",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}
