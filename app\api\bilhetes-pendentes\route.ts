import { NextRequest, NextResponse } from "next/server"
import { executeQuery } from "@/lib/database-config"

// Endpoint para listar bilhetes pendentes e facilitar testes
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status') || 'pendente'
    const limit = parseInt(searchParams.get('limit') || '20')
    const user_id = searchParams.get('user_id')

    console.log("📋 Listando bilhetes:", { status, limit, user_id })

    // Query base
    let query = `
      SELECT 
        id,
        codigo,
        status,
        transaction_id,
        pix_order_id,
        valor_total,
        created_at,
        updated_at,
        usuario_id
      FROM bilhetes
      WHERE 1=1
    `
    const params: any[] = []

    // Filtros
    if (status !== 'todos') {
      query += ` AND status = ?`
      params.push(status)
    }

    if (user_id) {
      query += ` AND usuario_id = ?`
      params.push(user_id)
    }

    // Ordenação e limite
    query += ` ORDER BY created_at DESC LIMIT ?`
    params.push(limit)

    const bilhetes = await executeQuery(query, params)

    // Estatísticas
    const stats = await executeQuery(`
      SELECT 
        status,
        COUNT(*) as count,
        SUM(valor_total) as total_valor
      FROM bilhetes
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
      GROUP BY status
    `)

    return NextResponse.json({
      success: true,
      message: "Bilhetes listados com sucesso",
      timestamp: new Date().toISOString(),
      filtros: {
        status: status,
        user_id: user_id,
        limit: limit
      },
      estatisticas: stats.reduce((acc: any, item: any) => {
        acc[item.status] = {
          count: item.count,
          total_valor: parseFloat(item.total_valor || 0)
        }
        return acc
      }, {}),
      bilhetes: bilhetes.map((b: any) => ({
        id: b.id,
        codigo: b.codigo,
        status: b.status,
        transaction_id: b.transaction_id,
        pix_order_id: b.pix_order_id,
        valor: parseFloat(b.valor_total),
        valor_formatado: `R$ ${parseFloat(b.valor_total).toFixed(2).replace('.', ',')}`,
        created_at: b.created_at,
        updated_at: b.updated_at,
        usuario_id: b.usuario_id,
        // Ações disponíveis
        acoes: {
          simular_pagamento: {
            url: `/api/simulate-pix-payment`,
            payload: {
              transaction_id: b.transaction_id,
              simulate_payment: true
            }
          },
          disparar_webhook: {
            url: `/api/webhook-trigger`,
            payload: {
              transaction_id: b.transaction_id,
              bilhete_codigo: b.codigo,
              force_trigger: true
            }
          },
          verificar_status: {
            url: `/api/pix/status?payment_id=${b.transaction_id}`
          }
        }
      })),
      total: bilhetes.length,
      endpoints_uteis: {
        simular_pagamento: "POST /api/simulate-pix-payment",
        disparar_webhook: "POST /api/webhook-trigger", 
        monitoramento_auto: "POST /api/auto-payment-monitor",
        teste_webhook: "POST /api/test-webhook-auto",
        webhook_principal: "POST /api/v1/MP/webhookruntransation"
      }
    })

  } catch (error) {
    console.error("❌ Erro ao listar bilhetes:", error)
    
    return NextResponse.json({
      error: "Erro ao listar bilhetes",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}

// Endpoint para ações em lote
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { acao, bilhetes_ids, user_id } = body

    console.log("🔄 Executando ação em lote:", { acao, bilhetes_ids, user_id })

    if (!acao || !bilhetes_ids || !Array.isArray(bilhetes_ids)) {
      return NextResponse.json({
        error: "acao e bilhetes_ids são obrigatórios"
      }, { status: 400 })
    }

    const resultados = []

    for (const bilhete_id of bilhetes_ids) {
      try {
        // Buscar bilhete
        const bilhete = await executeQuery(`
          SELECT * FROM bilhetes WHERE id = ? OR codigo = ? OR transaction_id = ?
          LIMIT 1
        `, [bilhete_id, bilhete_id, bilhete_id])

        if (!Array.isArray(bilhete) || bilhete.length === 0) {
          resultados.push({
            bilhete_id,
            success: false,
            error: "Bilhete não encontrado"
          })
          continue
        }

        const bilheteData = bilhete[0] as any

        let resultado = null

        switch (acao) {
          case 'simular_pagamento':
            const simResponse = await fetch(`${request.nextUrl.origin}/api/simulate-pix-payment`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                transaction_id: bilheteData.transaction_id,
                simulate_payment: true
              })
            })
            resultado = await simResponse.json()
            break

          case 'disparar_webhook':
            const webhookResponse = await fetch(`${request.nextUrl.origin}/api/webhook-trigger`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                transaction_id: bilheteData.transaction_id,
                bilhete_codigo: bilheteData.codigo,
                force_trigger: true
              })
            })
            resultado = await webhookResponse.json()
            break

          case 'verificar_status':
            const statusResponse = await fetch(`${request.nextUrl.origin}/api/pix/status?payment_id=${bilheteData.transaction_id}`)
            resultado = await statusResponse.json()
            break

          default:
            resultado = { error: "Ação não reconhecida" }
        }

        resultados.push({
          bilhete_id,
          bilhete_codigo: bilheteData.codigo,
          transaction_id: bilheteData.transaction_id,
          success: resultado?.success !== false,
          resultado
        })

      } catch (error) {
        resultados.push({
          bilhete_id,
          success: false,
          error: error instanceof Error ? error.message : String(error)
        })
      }
    }

    const sucessos = resultados.filter(r => r.success).length
    const falhas = resultados.filter(r => !r.success).length

    return NextResponse.json({
      success: true,
      message: `Ação '${acao}' executada em lote`,
      timestamp: new Date().toISOString(),
      estatisticas: {
        total_processados: resultados.length,
        sucessos,
        falhas,
        taxa_sucesso: `${((sucessos / resultados.length) * 100).toFixed(1)}%`
      },
      resultados
    })

  } catch (error) {
    console.error("❌ Erro na ação em lote:", error)
    
    return NextResponse.json({
      error: "Erro na ação em lote",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}
