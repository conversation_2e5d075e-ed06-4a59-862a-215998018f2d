#!/usr/bin/env node

// Script para monitorar bilhetes em tempo real
import fetch from 'node-fetch';

let lastCheck = new Date();
let knownBilhetes = new Set();

async function checkForNewBilhetes() {
  try {
    const response = await fetch('http://localhost:3000/api/debug-bilhete');
    const result = await response.json();
    
    if (result.success && result.bilhetes.length > 0) {
      const newBilhetes = result.bilhetes.filter(b => {
        const bilheteTime = new Date(b.created_at);
        return bilheteTime > lastCheck && !knownBilhetes.has(b.codigo);
      });
      
      if (newBilhetes.length > 0) {
        console.log(`\n🆕 ${newBilhetes.length} novo(s) bilhete(s) detectado(s):`);
        
        for (const bilhete of newBilhetes) {
          console.log(`\n📋 Bilhete: ${bilhete.codigo}`);
          console.log(`👤 Usuário: ${bilhete.usuario_nome}`);
          console.log(`💰 Valor: R$ ${bilhete.valor}`);
          console.log(`📊 Status: ${bilhete.status}`);
          console.log(`🕐 Criado: ${bilhete.created_at}`);
          
          knownBilhetes.add(bilhete.codigo);
          
          if (bilhete.status === 'pendente') {
            console.log(`\n💡 Para simular pagamento:`);
            console.log(`   node simulate-payment-webhook.js ${bilhete.codigo}`);
          }
        }
      }
      
      // Atualizar bilhetes conhecidos
      result.bilhetes.forEach(b => knownBilhetes.add(b.codigo));
    }
    
    lastCheck = new Date();
    
  } catch (error) {
    console.error('❌ Erro ao verificar bilhetes:', error.message);
  }
}

async function showCurrentStatus() {
  try {
    const response = await fetch('http://localhost:3000/api/debug-bilhete');
    const result = await response.json();
    
    if (result.success) {
      const pendentes = result.bilhetes.filter(b => b.status === 'pendente');
      const pagos = result.bilhetes.filter(b => b.status === 'pago');
      
      console.log(`\n📊 Status atual:`);
      console.log(`   💰 Pagos: ${pagos.length}`);
      console.log(`   ⏳ Pendentes: ${pendentes.length}`);
      console.log(`   📋 Total: ${result.bilhetes.length}`);
      
      if (pendentes.length > 0) {
        console.log(`\n⏳ Bilhetes pendentes:`);
        pendentes.forEach(b => {
          console.log(`   • ${b.codigo} - R$ ${b.valor} (${b.usuario_nome})`);
        });
      }
    }
  } catch (error) {
    console.error('❌ Erro ao obter status:', error.message);
  }
}

async function main() {
  console.log('🔍 Monitor de Bilhetes Iniciado');
  console.log('⏰ Verificando novos bilhetes a cada 5 segundos...');
  console.log('🛑 Pressione Ctrl+C para parar\n');
  
  // Mostrar status inicial
  await showCurrentStatus();
  
  // Inicializar bilhetes conhecidos
  try {
    const response = await fetch('http://localhost:3000/api/debug-bilhete');
    const result = await response.json();
    if (result.success) {
      result.bilhetes.forEach(b => knownBilhetes.add(b.codigo));
    }
  } catch (error) {
    console.error('❌ Erro na inicialização:', error.message);
  }
  
  // Monitorar continuamente
  setInterval(async () => {
    await checkForNewBilhetes();
  }, 5000);
  
  // Mostrar status a cada 30 segundos
  setInterval(async () => {
    console.log(`\n${'='.repeat(50)}`);
    console.log(`🕐 ${new Date().toLocaleString('pt-BR')}`);
    await showCurrentStatus();
  }, 30000);
}

// Executar
main().catch(console.error);
