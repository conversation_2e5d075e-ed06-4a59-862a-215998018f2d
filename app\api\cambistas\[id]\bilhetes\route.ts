import { type NextRequest, NextResponse } from "next/server"
import { executeQuery, executeQuerySingle } from "@/lib/database"

// Força renderização dinâmica
export const dynamic = 'force-dynamic'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cambistaId = parseInt(params.id)

    if (!cambistaId || isNaN(cambistaId)) {
      return NextResponse.json(
        { error: "ID do cambista inválido" },
        { status: 400 }
      )
    }

    console.log("🎫 Buscando bilhetes do cambista:", cambistaId)

    // Verificar se o usuário é realmente um cambista
    const cambista = await executeQuerySingle(
      "SELECT id, nome, email, tipo FROM usuarios WHERE id = ? AND tipo = 'cambista'",
      [cambistaId]
    )

    if (!cambista) {
      return NextResponse.json(
        { error: "Cambista não encontrado" },
        { status: 404 }
      )
    }

    // Buscar parâmetros de paginação
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const status = searchParams.get('status') || ''
    const offset = (page - 1) * limit

    // Construir query com filtros
    let whereClause = "WHERE cambista_id = ?"
    const queryParams: (number | string)[] = [cambistaId]

    if (status && status !== 'todos') {
      whereClause += " AND status = ?"
      queryParams.push(status)
    }

    // Buscar bilhetes com paginação
    const bilhetesQuery = `
      SELECT
        id,
        codigo,
        usuario_nome,
        usuario_email,
        valor_total,
        quantidade_apostas,
        status,
        created_at as data_criacao,
        transaction_id
      FROM bilhetes
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `

    queryParams.push(limit, offset)
    const bilhetes = await executeQuery(bilhetesQuery, queryParams)

    // Buscar total de bilhetes para paginação
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM bilhetes 
      ${whereClause}
    `

    const countParams = queryParams.slice(0, -2) // Remove limit e offset
    const totalResult = await executeQuerySingle(countQuery, countParams)
    const total = totalResult.total || 0

    // Calcular comissões para cada bilhete
    const porcentagemComissao = cambista.porcentagem_comissao || 10
    const bilhetesComComissao = bilhetes.map((bilhete: any) => ({
      ...bilhete,
      comissao_valor: bilhete.status === 'pago' ? 
        (parseFloat(bilhete.valor_total) * porcentagemComissao) / 100 : 0
    }))

    const resultado = {
      bilhetes: bilhetesComComissao,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1
      }
    }

    console.log(`✅ Encontrados ${bilhetes.length} bilhetes para o cambista`)

    return NextResponse.json(resultado)

  } catch (error: any) {
    console.error("❌ Erro ao buscar bilhetes do cambista:", error)
    
    return NextResponse.json(
      { 
        error: "Erro interno do servidor",
        message: "Não foi possível buscar os bilhetes"
      },
      { status: 500 }
    )
  }
}
