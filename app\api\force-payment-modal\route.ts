import { NextRequest, NextResponse } from "next/server"
import { executeQuery } from "@/lib/database-config"

// Endpoint para forçar o disparo do modal de pagamento confirmado
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { bilhete_codigo, transaction_id, user_id } = body

    console.log("🎭 Forçando modal de pagamento:", { bilhete_codigo, transaction_id, user_id })

    // Se não especificou bilhete, pegar o último bilhete pago do usuário
    let bilhete = null

    if (bilhete_codigo || transaction_id) {
      const bilhetes = await executeQuery(`
        SELECT * FROM bilhetes
        WHERE (codigo = ? OR transaction_id = ?)
        AND status = 'pago'
        LIMIT 1
      `, [bilhete_codigo || transaction_id, transaction_id || bilhete_codigo])
      
      bilhete = bilhetes[0]
    } else if (user_id) {
      const bilhetes = await executeQuery(`
        SELECT * FROM bilhetes
        WHERE usuario_id = ?
        AND status = 'pago'
        ORDER BY updated_at DESC
        LIMIT 1
      `, [user_id])
      
      bilhete = bilhetes[0]
    } else {
      // Pegar qualquer bilhete pago recente
      const bilhetes = await executeQuery(`
        SELECT * FROM bilhetes
        WHERE status = 'pago'
        ORDER BY updated_at DESC
        LIMIT 1
      `)
      
      bilhete = bilhetes[0]
    }

    if (!bilhete) {
      return NextResponse.json({
        error: "Nenhum bilhete pago encontrado",
        filtros: { bilhete_codigo, transaction_id, user_id }
      }, { status: 404 })
    }

    console.log("📋 Bilhete encontrado para modal:", {
      codigo: bilhete.codigo,
      status: bilhete.status,
      valor: bilhete.valor_total,
      transaction_id: bilhete.transaction_id
    })

    // Atualizar o updated_at para agora para simular pagamento recente
    await executeQuery(`
      UPDATE bilhetes 
      SET updated_at = NOW() 
      WHERE id = ?
    `, [bilhete.id])

    console.log("✅ Updated_at atualizado para simular pagamento recente")

    // Preparar dados do evento
    const eventData = {
      codigo: bilhete.codigo,
      valor: parseFloat(bilhete.valor_total),
      valor_formatado: `R$ ${parseFloat(bilhete.valor_total).toFixed(2).replace('.', ',')}`,
      data: new Date().toLocaleString('pt-BR'),
      transactionId: bilhete.transaction_id,
      timestamp: Date.now(),
      paymentConfirmed: true,
      forceModal: true
    }

    return NextResponse.json({
      success: true,
      message: "Modal de pagamento será disparado",
      timestamp: new Date().toISOString(),
      bilhete: {
        id: bilhete.id,
        codigo: bilhete.codigo,
        status: bilhete.status,
        transaction_id: bilhete.transaction_id,
        valor: parseFloat(bilhete.valor_total),
        valor_formatado: eventData.valor_formatado,
        updated_at: new Date().toISOString()
      },
      evento_disparado: eventData,
      instrucoes: [
        "✅ Bilhete atualizado com timestamp atual",
        "🔄 Sistema de verificação automática detectará a mudança",
        "🎉 Modal de sucesso aparecerá em até 5 segundos",
        "📱 Verifique a tela do usuário"
      ],
      como_funciona: {
        "1": "Updated_at foi atualizado para agora",
        "2": "usePixAutoCheck detectará mudança no próximo ciclo",
        "3": "Modal será disparado automaticamente",
        "4": "Evento pixPaymentConfirmed será enviado"
      }
    })

  } catch (error) {
    console.error("❌ Erro ao forçar modal:", error)
    
    return NextResponse.json({
      error: "Erro ao forçar modal de pagamento",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}

// Método GET para listar bilhetes que podem ter modal forçado
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const user_id = searchParams.get('user_id')
    const limit = parseInt(searchParams.get('limit') || '5')

    let query = `
      SELECT 
        id,
        codigo,
        status,
        transaction_id,
        valor_total,
        created_at,
        updated_at,
        usuario_id
      FROM bilhetes
      WHERE status = 'pago'
    `
    const params: any[] = []

    if (user_id) {
      query += ` AND usuario_id = ?`
      params.push(user_id)
    }

    query += ` ORDER BY updated_at DESC LIMIT ?`
    params.push(limit)

    const bilhetes = await executeQuery(query, params)

    return NextResponse.json({
      message: "Bilhetes pagos disponíveis para forçar modal",
      timestamp: new Date().toISOString(),
      filtros: { user_id, limit },
      bilhetes: bilhetes.map((b: any) => ({
        id: b.id,
        codigo: b.codigo,
        status: b.status,
        transaction_id: b.transaction_id,
        valor: parseFloat(b.valor_total),
        valor_formatado: `R$ ${parseFloat(b.valor_total).toFixed(2).replace('.', ',')}`,
        updated_at: b.updated_at,
        usuario_id: b.usuario_id,
        forcar_modal: {
          url: `/api/force-payment-modal`,
          payload: {
            bilhete_codigo: b.codigo,
            transaction_id: b.transaction_id
          }
        }
      })),
      total: bilhetes.length,
      como_usar: {
        forcar_modal_especifico: "POST /api/force-payment-modal",
        payload_exemplo: {
          bilhete_codigo: "BLT123...",
          transaction_id: "pixi_123...",
          user_id: "27 (opcional)"
        }
      },
      outros_endpoints: {
        simular_pagamento: "/api/simulate-pix-payment",
        disparar_webhook: "/api/webhook-trigger",
        listar_pendentes: "/api/bilhetes-pendentes"
      }
    })

  } catch (error) {
    return NextResponse.json({
      error: "Erro ao listar bilhetes",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}
