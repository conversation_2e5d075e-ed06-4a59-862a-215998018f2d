#!/usr/bin/env node

/**
 * Script para criar um bolão de teste
 */

import { executeQuery } from '../lib/database-config.js'

async function createTestBolao() {
  try {
    console.log('🎲 Criando bolão de teste...')

    // Verificar se já existe um bolão
    const existingBoloes = await executeQuery('SELECT COUNT(*) as count FROM boloes')
    
    if (existingBoloes[0].count > 0) {
      console.log('✅ Já existem bolões no banco')
      
      const boloes = await executeQuery('SELECT id, nome, status FROM boloes ORDER BY id')
      console.log('📋 Bolões existentes:')
      boloes.forEach(bolao => {
        console.log(`   - ID: ${bolao.id}, Nome: ${bolao.nome}, Status: ${bolao.status}`)
      })
      return
    }

    // Verificar se existe campeonato
    let campeonatos = await executeQuery('SELECT id FROM campeonatos LIMIT 1')
    let campeonatoId = 1

    if (campeonatos.length === 0) {
      console.log('📝 Criando campeonato de teste...')
      await executeQuery(`
        INSERT INTO campeonatos (id, nome, codigo, pais, temporada, ativo) 
        VALUES (1, 'Campeonato Brasileiro', 'BSA', 'Brasil', '2024', 1)
      `)
      campeonatoId = 1
    } else {
      campeonatoId = campeonatos[0].id
    }

    // Criar bolão de teste
    console.log('🎯 Criando bolão de teste...')
    const result = await executeQuery(`
      INSERT INTO boloes (
        nome, 
        descricao, 
        valor_aposta, 
        data_inicio, 
        data_fim, 
        status, 
        campeonato_id, 
        created_by
      ) VALUES (
        'Bolão Teste', 
        'Bolão criado para testes do sistema', 
        0.50, 
        '2024-01-01 00:00:00', 
        '2024-12-31 23:59:59', 
        'ativo', 
        ?, 
        1
      )
    `, [campeonatoId])

    const bolaoId = result.insertId

    console.log(`✅ Bolão criado com sucesso! ID: ${bolaoId}`)

    // Criar alguns times se não existirem
    const times = await executeQuery('SELECT COUNT(*) as count FROM times')
    
    if (times[0].count === 0) {
      console.log('⚽ Criando times de teste...')
      
      const timesData = [
        ['Flamengo', 'FLA', campeonatoId],
        ['Palmeiras', 'PAL', campeonatoId],
        ['São Paulo', 'SAO', campeonatoId],
        ['Corinthians', 'COR', campeonatoId]
      ]

      for (const [nome, codigo, campId] of timesData) {
        await executeQuery(`
          INSERT INTO times (nome, codigo, campeonato_id, ativo) 
          VALUES (?, ?, ?, 1)
        `, [nome, codigo, campId])
      }

      console.log('✅ Times criados com sucesso!')
    }

    // Criar alguns jogos de teste
    const jogos = await executeQuery('SELECT COUNT(*) as count FROM jogos')
    
    if (jogos[0].count === 0) {
      console.log('🏟️ Criando jogos de teste...')
      
      const timesIds = await executeQuery('SELECT id FROM times LIMIT 4')
      
      if (timesIds.length >= 4) {
        const jogosData = [
          [timesIds[0].id, timesIds[1].id, '2025-07-25 20:00:00'],
          [timesIds[2].id, timesIds[3].id, '2025-07-26 18:30:00'],
          [timesIds[0].id, timesIds[2].id, '2025-07-27 16:00:00']
        ]

        for (const [casaId, foraId, dataJogo] of jogosData) {
          await executeQuery(`
            INSERT INTO jogos (
              campeonato_id, 
              bolao_id, 
              time_casa_id, 
              time_fora_id, 
              data_jogo, 
              status, 
              ativo_apostas
            ) VALUES (?, ?, ?, ?, ?, 'agendado', 1)
          `, [campeonatoId, bolaoId, casaId, foraId, dataJogo])
        }

        console.log('✅ Jogos criados com sucesso!')
      }
    }

    // Verificar resultado final
    const finalBoloes = await executeQuery(`
      SELECT 
        b.id, 
        b.nome, 
        b.status, 
        b.valor_aposta,
        c.nome as campeonato_nome,
        COUNT(j.id) as total_jogos
      FROM boloes b
      LEFT JOIN campeonatos c ON b.campeonato_id = c.id
      LEFT JOIN jogos j ON b.id = j.bolao_id
      WHERE b.id = ?
      GROUP BY b.id
    `, [bolaoId])

    if (finalBoloes.length > 0) {
      const bolao = finalBoloes[0]
      console.log('')
      console.log('🎉 BOLÃO CRIADO COM SUCESSO!')
      console.log('================================')
      console.log(`📋 ID: ${bolao.id}`)
      console.log(`🎯 Nome: ${bolao.nome}`)
      console.log(`📊 Status: ${bolao.status}`)
      console.log(`💰 Valor: R$ ${parseFloat(bolao.valor_aposta).toFixed(2)}`)
      console.log(`🏆 Campeonato: ${bolao.campeonato_nome}`)
      console.log(`⚽ Jogos: ${bolao.total_jogos}`)
      console.log('')
      console.log('🧪 AGORA VOCÊ PODE TESTAR:')
      console.log(`   • Deletar: curl -X DELETE http://localhost:3000/api/admin/boloes/${bolao.id}`)
      console.log(`   • Atualizar: curl -X PATCH http://localhost:3000/api/admin/boloes/${bolao.id}`)
      console.log(`   • Listar: curl http://localhost:3000/api/admin/boloes/list`)
    }

  } catch (error) {
    console.error('❌ Erro ao criar bolão de teste:', error)
    throw error
  }
}

// Executar
createTestBolao()
  .then(() => {
    console.log('✅ Script concluído com sucesso!')
    process.exit(0)
  })
  .catch(error => {
    console.error('💥 Erro fatal:', error)
    process.exit(1)
  })
