#!/usr/bin/env node

/**
 * Script para testar a conexão com o banco de dados MySQL
 * Execute: npm run db:test
 */

import mysql from 'mysql2/promise'
import { config } from 'dotenv'

// Load environment variables
config({ path: '.env.local' })

// Configuração do banco de dados
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'sistema-bolao-top',
  charset: 'utf8mb4'
}

async function testConnection() {
  let connection = null
  
  try {
    console.log('🔄 Testando conexão com MySQL...')
    console.log(`📍 Host: ${dbConfig.host}:${dbConfig.port}`)
    console.log(`👤 Usuário: ${dbConfig.user}`)
    console.log(`🗄️  Banco: ${dbConfig.database}`)
    console.log('=' .repeat(50))
    
    // Tentar conectar
    connection = await mysql.createConnection(dbConfig)
    
    console.log('✅ Conexão estabelecida com sucesso!')
    
    // Testar uma query simples (corrigida para MariaDB)
    const [result] = await connection.execute('SELECT 1 as test, NOW() as current_time')
    console.log('✅ Query de teste executada:', result[0])
    
    // Verificar se o banco existe
    const [databases] = await connection.execute('SHOW DATABASES LIKE ?', [dbConfig.database])
    if (databases.length > 0) {
      console.log('✅ Banco de dados encontrado!')
      
      // Listar tabelas
      const [tables] = await connection.execute('SHOW TABLES')
      console.log(`📋 Tabelas encontradas (${tables.length}):`)
      tables.forEach(table => {
        const tableName = Object.values(table)[0]
        console.log(`  - ${tableName}`)
      })
      
      // Verificar algumas tabelas principais
      const mainTables = ['usuarios', 'campeonatos', 'times', 'jogos', 'boloes']
      for (const tableName of mainTables) {
        try {
          const [count] = await connection.execute(`SELECT COUNT(*) as count FROM ${tableName}`)
          console.log(`  📊 ${tableName}: ${count[0].count} registros`)
        } catch (error) {
          console.log(`  ❌ ${tableName}: Tabela não encontrada`)
        }
      }
      
    } else {
      console.log('⚠️  Banco de dados não encontrado!')
      console.log('💡 Execute: npm run db:setup')
    }
    
    console.log('\n🎉 Teste de conexão concluído com sucesso!')
    
  } catch (error) {
    console.error('❌ Erro na conexão:', error.message)
    
    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.error('\n💡 Erro de acesso negado:')
      console.error('   - Verifique o usuário e senha no .env.local')
      console.error('   - Certifique-se que o usuário tem permissões')
    } else if (error.code === 'ECONNREFUSED') {
      console.error('\n💡 Conexão recusada:')
      console.error('   - Verifique se o MySQL está rodando')
      console.error('   - Verifique o host e porta no .env.local')
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.error('\n💡 Banco de dados não existe:')
      console.error('   - Execute: npm run db:setup')
    }
    
    process.exit(1)
  } finally {
    if (connection) {
      await connection.end()
    }
  }
}

// Verificar variáveis de ambiente
function checkEnvironment() {
  console.log('🔍 Verificando configuração...')
  
  const vars = {
    'DB_HOST': process.env.DB_HOST,
    'DB_PORT': process.env.DB_PORT,
    'DB_USER': process.env.DB_USER,
    'DB_PASSWORD': process.env.DB_PASSWORD ? '***' : '(vazio)',
    'DB_NAME': process.env.DB_NAME
  }
  
  Object.entries(vars).forEach(([key, value]) => {
    console.log(`  ${key}: ${value || '(não definido)'}`)
  })
  
  console.log('')
}

// Executar o teste
async function main() {
  console.log('🧪 Teste de Conexão MySQL - Sistema Bolão')
  console.log('=' .repeat(50))
  
  checkEnvironment()
  await testConnection()
}

main().catch(console.error)
