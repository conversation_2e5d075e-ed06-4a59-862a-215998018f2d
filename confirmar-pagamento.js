#!/usr/bin/env node

// Confirma pagamento PIX quando webhook remoto falha
import fetch from 'node-fetch';

async function confirmarPagamento() {
  const transactionId = process.argv[2];

  if (!transactionId) {
    console.log('💳 CONFIRMAR PAGAMENTO PIX');
    console.log('=' .repeat(40));
    console.log('');
    console.log('📝 USO:');
    console.log('  node confirmar-pagamento.js <TRANSACTION_ID>');
    console.log('');
    console.log('📋 EXEMPLO:');
    console.log('  node confirmar-pagamento.js pixi_01k0qcv6bsepkamvv5h9nmsvtc');
    console.log('');
    console.log('💡 QUANDO USAR:');
    console.log('  - Usuário fez pagamento PIX real');
    console.log('  - Webhook remoto não funcionou (502 Bad Gateway)');
    console.log('  - Status ainda está "pendente" na interface');
    console.log('');
    return;
  }

  console.log('💳 CONFIRMANDO PAGAMENTO PIX');
  console.log('=' .repeat(40));
  console.log(`🔗 Transaction ID: ${transactionId}`);

  try {
    // 1. Verificar status atual
    console.log('\n🔍 1. Verificando status atual...');
    const statusResponse = await fetch(`http://localhost:3000/api/pix/check-status?transaction_id=${transactionId}`);
    const statusData = await statusResponse.json();
    
    if (!statusData.success) {
      console.log('❌ Transaction ID não encontrado:', transactionId);
      return;
    }

    console.log(`🎫 Bilhete: ${statusData.codigo}`);
    console.log(`💰 Valor: R$ ${statusData.valor}`);
    console.log(`📊 Status atual: ${statusData.status}`);

    if (statusData.status === 'pago') {
      console.log('\n✅ BILHETE JÁ ESTÁ PAGO!');
      console.log('🎉 Não é necessário confirmar novamente');
      return;
    }

    // 2. Tentar webhook remoto primeiro
    console.log('\n🌐 2. Tentando webhook remoto...');
    try {
      const remoteResponse = await fetch('https://ouroemu.site/api/v1/MP/webhookruntransation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          order_id: transactionId,
          status: 'PAID',
          type: 'PIXOUT',
          message: 'Payment confirmed manually'
        }),
        timeout: 10000
      });

      if (remoteResponse.ok) {
        const remoteResult = await remoteResponse.json();
        console.log('✅ Webhook remoto funcionou!');
        console.log(`📊 Resultado: ${remoteResult.message || 'Sucesso'}`);
        return;
      } else {
        console.log(`⚠️ Webhook remoto falhou: ${remoteResponse.status} ${remoteResponse.statusText}`);
      }
    } catch (error) {
      console.log(`⚠️ Webhook remoto indisponível: ${error.message}`);
    }

    // 3. Usar webhook local como fallback
    console.log('\n🏠 3. Usando webhook local...');
    const localResponse = await fetch('http://localhost:3000/api/v1/MP/webhookruntransation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        order_id: transactionId,
        status: 'PAID',
        type: 'PIXOUT',
        message: 'Payment confirmed manually via local webhook'
      })
    });

    const localResult = await localResponse.json();

    if (localResult.success) {
      console.log('✅ PAGAMENTO CONFIRMADO COM SUCESSO!');
      console.log(`🎫 Bilhete: ${localResult.bilhete_atualizado?.codigo || 'N/A'}`);
      console.log(`📊 Status anterior: ${localResult.bilhete_original?.status || 'N/A'}`);
      console.log(`📊 Status atual: ${localResult.bilhete_atualizado?.status || 'N/A'}`);
      
      // 4. Verificar se foi realmente atualizado
      console.log('\n🔍 4. Verificando atualização...');
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const finalStatusResponse = await fetch(`http://localhost:3000/api/pix/check-status?transaction_id=${transactionId}`);
      const finalStatusData = await finalStatusResponse.json();
      
      console.log(`📊 Status final: ${finalStatusData.status}`);
      
      if (finalStatusData.status === 'pago') {
        console.log('\n🎉 SUCESSO TOTAL!');
        console.log('✅ Pagamento confirmado no banco');
        console.log('✅ Modal de sucesso deve aparecer na interface');
        console.log('✅ Usuário pode ver bilhete como pago');
        
        console.log('\n💡 PRÓXIMOS PASSOS:');
        console.log('1. 🌐 Acesse http://localhost:3000');
        console.log('2. 🔄 Atualize a página se necessário');
        console.log('3. 🎉 Veja o modal de sucesso');
        console.log('4. 📊 Verifique o status do bilhete');
      } else {
        console.log('\n⚠️ PROBLEMA NA ATUALIZAÇÃO');
        console.log('❌ Status não foi atualizado corretamente');
        console.log('🔧 Verifique os logs do servidor');
      }
      
    } else {
      console.log('❌ ERRO NO WEBHOOK LOCAL:', localResult.message);
      console.log('🔧 Verifique se o servidor está funcionando');
    }

  } catch (error) {
    console.error('❌ Erro:', error.message);
  }

  console.log('\n' + '=' .repeat(40));
  console.log('🏁 Confirmação finalizada');
}

// Executar
confirmarPagamento().catch(console.error);
