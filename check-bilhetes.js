#!/usr/bin/env node

// Script para verificar bilhetes no banco de dados
import { executeQuery, initializeDatabase } from './lib/database-config.js';

async function checkBilhetes() {
  try {
    console.log('🔍 Verificando bilhetes no banco de dados...\n');
    
    await initializeDatabase();
    
    // Buscar todos os bilhetes recentes
    const bilhetes = await executeQuery(`
      SELECT 
        id, 
        codigo, 
        transaction_id, 
        pix_order_id,
        status, 
        valor_total,
        usuario_nome,
        created_at,
        updated_at
      FROM bilhetes 
      ORDER BY created_at DESC 
      LIMIT 10
    `);
    
    console.log(`📊 Total de bilhetes encontrados: ${bilhetes.length}\n`);
    
    if (bilhetes.length === 0) {
      console.log('❌ Nenhum bilhete encontrado no banco de dados');
      return;
    }
    
    console.log('📋 Bilhetes encontrados:');
    console.log('='.repeat(80));
    
    bilhetes.forEach((bilhete, index) => {
      console.log(`${index + 1}. Bilhete ID: ${bilhete.id}`);
      console.log(`   Código: ${bilhete.codigo}`);
      console.log(`   Transaction ID: ${bilhete.transaction_id || 'N/A'}`);
      console.log(`   PIX Order ID: ${bilhete.pix_order_id || 'N/A'}`);
      console.log(`   Status: ${bilhete.status}`);
      console.log(`   Valor: R$ ${parseFloat(bilhete.valor_total).toFixed(2)}`);
      console.log(`   Usuário: ${bilhete.usuario_nome}`);
      console.log(`   Criado em: ${bilhete.created_at}`);
      console.log(`   Atualizado em: ${bilhete.updated_at}`);
      console.log('-'.repeat(40));
    });
    
    // Buscar especificamente o bilhete que está sendo testado
    console.log('\n🎯 Buscando bilhete específico: BLT175312542333427L1NORV');
    
    const bilheteEspecifico = await executeQuery(`
      SELECT * FROM bilhetes 
      WHERE codigo = ? OR transaction_id = ? OR pix_order_id = ?
      LIMIT 1
    `, ['BLT175312542333427L1NORV', 'BLT175312542333427L1NORV', 'BLT175312542333427L1NORV']);
    
    if (bilheteEspecifico.length > 0) {
      console.log('✅ Bilhete encontrado:');
      console.log(JSON.stringify(bilheteEspecifico[0], null, 2));
    } else {
      console.log('❌ Bilhete BLT175312542333427L1NORV não encontrado');
      
      // Buscar bilhetes similares
      const similares = await executeQuery(`
        SELECT codigo, transaction_id, status FROM bilhetes 
        WHERE codigo LIKE '%175312542333427%' OR transaction_id LIKE '%175312542333427%'
      `);
      
      if (similares.length > 0) {
        console.log('🔍 Bilhetes similares encontrados:');
        similares.forEach(b => {
          console.log(`   - Código: ${b.codigo}, Transaction: ${b.transaction_id}, Status: ${b.status}`);
        });
      }
    }
    
    // Verificar estrutura da tabela
    console.log('\n🏗️ Estrutura da tabela bilhetes:');
    const estrutura = await executeQuery('DESCRIBE bilhetes');
    estrutura.forEach(col => {
      console.log(`   ${col.Field}: ${col.Type} ${col.Null === 'YES' ? '(NULL)' : '(NOT NULL)'} ${col.Key ? `[${col.Key}]` : ''}`);
    });
    
  } catch (error) {
    console.error('❌ Erro ao verificar bilhetes:', error);
  }
}

// Executar verificação
checkBilhetes().catch(console.error);
