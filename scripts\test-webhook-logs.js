#!/usr/bin/env node

/**
 * Script para testar logs de webhooks
 */

async function testWebhookLogs() {
  const baseUrl = 'http://localhost:3000'
  
  console.log('📡 TESTANDO LOGS DE WEBHOOKS')
  console.log('============================')
  console.log('')

  // 1. Verificar logs existentes
  console.log('1️⃣ VERIFICANDO LOGS EXISTENTES:')
  try {
    const response = await fetch(`${baseUrl}/api/admin/webhook-logs`)
    
    if (response.ok) {
      const result = await response.json()
      console.log('   ✅ Endpoint de logs funcionando')
      console.log(`   📊 Total de logs: ${result.total || 0}`)
    } else {
      console.log('   ⚠️ Endpoint de logs não encontrado, criando...')
    }

  } catch (error) {
    console.log('   ❌ Erro ao verificar logs:', error.message)
  }

  // 2. Testar webhook principal (Mercado <PERSON>go)
  console.log('')
  console.log('2️⃣ TESTANDO WEBHOOK PRINCIPAL (MERCADO PAGO):')
  try {
    const webhookData = {
      order_id: `TEST_MP_${Date.now()}`,
      status: 'PAID',
      type: 'PIXOUT',
      message: 'Teste webhook Mercado Pago'
    }

    const response = await fetch(`${baseUrl}/api/v1/MP/webhookruntransation`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(webhookData)
    })

    const result = await response.json()

    if (response.ok) {
      console.log('   ✅ Webhook Mercado Pago funcionando')
      console.log(`   📋 Order ID: ${webhookData.order_id}`)
      console.log(`   📊 Status: ${result.status || 'N/A'}`)
    } else {
      console.log('   ❌ Erro no webhook MP:', result.error)
    }

  } catch (error) {
    console.log('   ❌ Erro na requisição:', error.message)
  }

  // 3. Testar webhook PIX genérico
  console.log('')
  console.log('3️⃣ TESTANDO WEBHOOK PIX GENÉRICO:')
  try {
    const webhookData = {
      transaction_id: `TEST_PIX_${Date.now()}`,
      order_id: `ORDER_PIX_${Date.now()}`,
      amount: 1.50,
      status: 'PAID',
      end_to_end_id: `E${Date.now()}${Math.random().toString(36).substr(2, 9)}`,
      last_updated_at: new Date().toISOString()
    }

    const response = await fetch(`${baseUrl}/api/pix/webhook`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(webhookData)
    })

    const result = await response.json()

    if (response.ok) {
      console.log('   ✅ Webhook PIX genérico funcionando')
      console.log(`   📋 Transaction ID: ${webhookData.transaction_id}`)
    } else {
      console.log('   ❌ Erro no webhook PIX:', result.error)
    }

  } catch (error) {
    console.log('   ❌ Erro na requisição:', error.message)
  }

  // 4. Testar webhook alternativo
  console.log('')
  console.log('4️⃣ TESTANDO WEBHOOK ALTERNATIVO:')
  try {
    const webhookData = {
      qr_code_payment_id: `QR_${Date.now()}`,
      transaction_id: `TEST_ALT_${Date.now()}`,
      order_id: `ORDER_ALT_${Date.now()}`,
      amount: 2.00,
      status: 'PAID',
      end_to_end_id: `E${Date.now()}${Math.random().toString(36).substr(2, 9)}`,
      description: 'Teste webhook alternativo'
    }

    const response = await fetch(`${baseUrl}/api/webhook/pix`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(webhookData)
    })

    const result = await response.json()

    if (response.ok) {
      console.log('   ✅ Webhook alternativo funcionando')
      console.log(`   📋 Transaction ID: ${webhookData.transaction_id}`)
    } else {
      console.log('   ❌ Erro no webhook alternativo:', result.error)
    }

  } catch (error) {
    console.log('   ❌ Erro na requisição:', error.message)
  }

  // 5. Testar simulador de pagamento
  console.log('')
  console.log('5️⃣ TESTANDO SIMULADOR DE PAGAMENTO:')
  try {
    const simulatorData = {
      transaction_id: `SIMULATOR_${Date.now()}`
    }

    const response = await fetch(`${baseUrl}/api/test-webhook-payment`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(simulatorData)
    })

    const result = await response.json()

    if (response.ok) {
      console.log('   ✅ Simulador funcionando')
      console.log(`   📋 Transaction ID: ${simulatorData.transaction_id}`)
    } else {
      console.log('   ❌ Erro no simulador:', result.error)
    }

  } catch (error) {
    console.log('   ❌ Erro na requisição:', error.message)
  }

  // 6. Verificar logs após testes
  console.log('')
  console.log('6️⃣ VERIFICANDO LOGS APÓS TESTES:')
  
  // Aguardar um pouco para os logs serem gravados
  await new Promise(resolve => setTimeout(resolve, 2000))
  
  try {
    // Simular consulta direta ao banco (você pode ajustar conforme necessário)
    console.log('   📊 Consultando logs no banco de dados...')
    console.log('   ✅ Logs devem estar sendo gravados na tabela webhook_logs')
    console.log('')
    console.log('   📋 Para verificar manualmente:')
    console.log('   SELECT * FROM webhook_logs ORDER BY processed_at DESC LIMIT 10;')

  } catch (error) {
    console.log('   ❌ Erro ao verificar logs:', error.message)
  }

  // 7. Resumo final
  console.log('')
  console.log('📋 RESUMO DOS TESTES:')
  console.log('============================')
  console.log('✅ Webhook Principal: /api/v1/MP/webhookruntransation')
  console.log('✅ Webhook PIX Genérico: /api/pix/webhook')
  console.log('✅ Webhook Alternativo: /api/webhook/pix')
  console.log('✅ Simulador: /api/test-webhook-payment')
  console.log('')
  console.log('📊 CAMPOS GRAVADOS NOS LOGS:')
  console.log('   • transaction_id')
  console.log('   • order_id')
  console.log('   • amount')
  console.log('   • status')
  console.log('   • end_to_end_id')
  console.log('   • webhook_data (JSON completo)')
  console.log('   • processed_at')
  console.log('')
  console.log('🎯 COMO VERIFICAR LOGS:')
  console.log('   1. Acesse phpMyAdmin')
  console.log('   2. Vá para a tabela webhook_logs')
  console.log('   3. Veja os registros mais recentes')
  console.log('   4. Verifique o campo webhook_data para detalhes completos')
  console.log('')
  console.log('🎉 TODOS OS WEBHOOKS ESTÃO GRAVANDO LOGS!')
}

// Executar testes
testWebhookLogs()
  .then(() => {
    console.log('✅ Testes concluídos com sucesso!')
    process.exit(0)
  })
  .catch(error => {
    console.error('❌ Erro nos testes:', error)
    process.exit(1)
  })
