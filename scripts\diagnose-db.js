#!/usr/bin/env node

/**
 * Script para diagnosticar problemas de banco de dados
 * Execute: node scripts/diagnose-db.js
 */

import mysql from 'mysql2/promise'
import { config } from 'dotenv'

// Load environment variables
config({ path: '.env.local' })

async function diagnose() {
  console.log('🔍 Diagnóstico do Banco de Dados')
  console.log('=' .repeat(50))
  
  // Mostrar configuração atual
  console.log('📋 Configuração atual:')
  console.log(`  DB_HOST: ${process.env.DB_HOST || 'localhost'}`)
  console.log(`  DB_PORT: ${process.env.DB_PORT || '3306'}`)
  console.log(`  DB_USER: ${process.env.DB_USER || 'root'}`)
  console.log(`  DB_PASSWORD: ${process.env.DB_PASSWORD ? '***' : '(vazio)'}`)
  console.log(`  DB_NAME: ${process.env.DB_NAME || 'sistema-bolao-top'}`)
  console.log('')
  
  let connection = null
  
  try {
    // Testar conexão
    const dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '3306'),
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'sistema-bolao-top',
      charset: 'utf8mb4'
    }
    
    console.log('🔄 Testando conexão...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Conexão estabelecida')
    
    // Verificar usuário atual
    const [userInfo] = await connection.execute('SELECT USER() as current_user, DATABASE() as current_db')
    console.log(`👤 Usuário conectado: ${userInfo[0].current_user}`)
    console.log(`🗄️  Banco atual: ${userInfo[0].current_db}`)
    
    // Verificar permissões
    console.log('\n🔐 Verificando permissões...')
    try {
      const [grants] = await connection.execute(`SHOW GRANTS FOR CURRENT_USER()`)
      console.log('📋 Permissões atuais:')
      grants.forEach(grant => {
        console.log(`  - ${Object.values(grant)[0]}`)
      })
    } catch (error) {
      console.log('⚠️  Não foi possível verificar permissões:', error.message)
    }
    
    // Testar operações básicas
    console.log('\n🧪 Testando operações...')
    
    // SELECT
    try {
      await connection.execute('SELECT 1')
      console.log('✅ SELECT: OK')
    } catch (error) {
      console.log('❌ SELECT: ERRO -', error.message)
    }
    
    // INSERT (teste)
    try {
      await connection.execute('SELECT COUNT(*) FROM boloes')
      console.log('✅ Acesso à tabela boloes: OK')
    } catch (error) {
      console.log('❌ Acesso à tabela boloes: ERRO -', error.message)
    }
    
    // DELETE (teste sem executar)
    try {
      await connection.execute('EXPLAIN DELETE FROM boloes WHERE id = 999999')
      console.log('✅ Permissão DELETE: OK')
    } catch (error) {
      console.log('❌ Permissão DELETE: ERRO -', error.message)
      
      if (error.code === 'ER_TABLEACCESS_DENIED_ERROR') {
        console.log('💡 Solução: Execute o script de correção de permissões')
        console.log('   node scripts/fix-mysql-permissions.js')
      }
    }
    
    console.log('\n🎯 Diagnóstico concluído!')
    
  } catch (error) {
    console.error('❌ Erro na conexão:', error.message)
    
    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.error('\n💡 Erro de acesso negado:')
      console.error('   - Verifique o usuário e senha no .env.local')
      console.error('   - Certifique-se que o usuário tem permissões')
    } else if (error.code === 'ECONNREFUSED') {
      console.error('\n💡 Conexão recusada:')
      console.error('   - Verifique se o MySQL está rodando')
      console.error('   - Verifique o host e porta no .env.local')
    }
    
  } finally {
    if (connection) {
      await connection.end()
    }
  }
}

diagnose().catch(console.error)
