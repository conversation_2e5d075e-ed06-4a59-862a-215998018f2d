-- =====================================================
-- SISTEMA BOLÃO - SQL COMPLETO PARA PHPMYADMIN
-- =====================================================
-- Data: 2025-07-21
-- Versão: 1.0
-- Descrição: Estrutura completa do banco de dados
-- =====================================================

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- =====================================================
-- CONFIGURAÇÕES INICIAIS
-- =====================================================

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

-- =====================================================
-- CRIAÇÃO DO BANCO DE DADOS
-- =====================================================

CREATE DATABASE IF NOT EXISTS `sistema_bolao` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `sistema_bolao`;

-- =====================================================
-- TABELA: usuarios
-- =====================================================

DROP TABLE IF EXISTS `usuarios`;
CREATE TABLE `usuarios` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nome` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `telefone` varchar(20) DEFAULT NULL,
  `cpf` varchar(14) DEFAULT NULL,
  `senha` varchar(255) NOT NULL,
  `tipo` enum('usuario','cambista','admin') DEFAULT 'usuario',
  `status` enum('ativo','inativo','suspenso') DEFAULT 'ativo',
  `codigo_afiliado` varchar(50) DEFAULT NULL,
  `afiliado_por` int(11) DEFAULT NULL,
  `saldo` decimal(10,2) DEFAULT 0.00,
  `comissao_percentual` decimal(5,2) DEFAULT 0.00,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`),
  UNIQUE KEY `cpf` (`cpf`),
  UNIQUE KEY `codigo_afiliado` (`codigo_afiliado`),
  KEY `afiliado_por` (`afiliado_por`),
  CONSTRAINT `usuarios_ibfk_1` FOREIGN KEY (`afiliado_por`) REFERENCES `usuarios` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABELA: campeonatos
-- =====================================================

DROP TABLE IF EXISTS `campeonatos`;
CREATE TABLE `campeonatos` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nome` varchar(255) NOT NULL,
  `codigo` varchar(50) NOT NULL,
  `pais` varchar(100) DEFAULT NULL,
  `temporada` varchar(20) DEFAULT NULL,
  `logo_url` text DEFAULT NULL,
  `ativo` tinyint(1) DEFAULT 1,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `codigo` (`codigo`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABELA: times
-- =====================================================

DROP TABLE IF EXISTS `times`;
CREATE TABLE `times` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nome` varchar(255) NOT NULL,
  `codigo` varchar(50) DEFAULT NULL,
  `logo_url` text DEFAULT NULL,
  `pais` varchar(100) DEFAULT NULL,
  `campeonato_id` int(11) DEFAULT NULL,
  `ativo` tinyint(1) DEFAULT 1,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `campeonato_id` (`campeonato_id`),
  CONSTRAINT `times_ibfk_1` FOREIGN KEY (`campeonato_id`) REFERENCES `campeonatos` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABELA: boloes
-- =====================================================

DROP TABLE IF EXISTS `boloes`;
CREATE TABLE `boloes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nome` varchar(255) NOT NULL,
  `descricao` text DEFAULT NULL,
  `valor_aposta` decimal(10,2) NOT NULL DEFAULT 1.00,
  `data_inicio` datetime DEFAULT NULL,
  `data_fim` datetime DEFAULT NULL,
  `status` enum('ativo','inativo','finalizado') DEFAULT 'ativo',
  `banner_image` text DEFAULT NULL,
  `campeonato_id` int(11) DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `campeonato_id` (`campeonato_id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `boloes_ibfk_1` FOREIGN KEY (`campeonato_id`) REFERENCES `campeonatos` (`id`) ON DELETE SET NULL,
  CONSTRAINT `boloes_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `usuarios` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABELA: jogos
-- =====================================================

DROP TABLE IF EXISTS `jogos`;
CREATE TABLE `jogos` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `external_id` varchar(100) DEFAULT NULL,
  `campeonato_id` int(11) NOT NULL,
  `bolao_id` int(11) DEFAULT NULL,
  `time_casa_id` int(11) NOT NULL,
  `time_fora_id` int(11) NOT NULL,
  `data_jogo` datetime NOT NULL,
  `rodada` varchar(50) DEFAULT NULL,
  `status` enum('agendado','ao_vivo','finalizado','adiado','cancelado') DEFAULT 'agendado',
  `gols_casa` int(11) DEFAULT NULL,
  `gols_fora` int(11) DEFAULT NULL,
  `resultado` enum('casa','empate','fora') DEFAULT NULL,
  `ativo_apostas` tinyint(1) DEFAULT 1,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `external_id` (`external_id`),
  KEY `campeonato_id` (`campeonato_id`),
  KEY `bolao_id` (`bolao_id`),
  KEY `time_casa_id` (`time_casa_id`),
  KEY `time_fora_id` (`time_fora_id`),
  KEY `data_jogo` (`data_jogo`),
  CONSTRAINT `jogos_ibfk_1` FOREIGN KEY (`campeonato_id`) REFERENCES `campeonatos` (`id`) ON DELETE CASCADE,
  CONSTRAINT `jogos_ibfk_2` FOREIGN KEY (`bolao_id`) REFERENCES `boloes` (`id`) ON DELETE SET NULL,
  CONSTRAINT `jogos_ibfk_3` FOREIGN KEY (`time_casa_id`) REFERENCES `times` (`id`) ON DELETE CASCADE,
  CONSTRAINT `jogos_ibfk_4` FOREIGN KEY (`time_fora_id`) REFERENCES `times` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABELA: bilhetes
-- =====================================================

DROP TABLE IF EXISTS `bilhetes`;
CREATE TABLE `bilhetes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `codigo` varchar(50) NOT NULL,
  `usuario_id` int(11) NOT NULL,
  `bolao_id` int(11) DEFAULT NULL,
  `cambista_id` int(11) DEFAULT NULL,
  `valor_total` decimal(10,2) NOT NULL,
  `quantidade_apostas` int(11) DEFAULT 0,
  `status` enum('pendente','pago','cancelado','expirado') DEFAULT 'pendente',
  `qr_code_pix` text DEFAULT NULL,
  `transaction_id` varchar(255) DEFAULT NULL,
  `pix_order_id` varchar(255) DEFAULT NULL,
  `data_expiracao` datetime DEFAULT NULL,
  `usuario_nome` varchar(255) DEFAULT NULL,
  `usuario_email` varchar(255) DEFAULT NULL,
  `usuario_telefone` varchar(20) DEFAULT NULL,
  `usuario_cpf` varchar(14) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `codigo` (`codigo`),
  UNIQUE KEY `transaction_id` (`transaction_id`),
  KEY `usuario_id` (`usuario_id`),
  KEY `bolao_id` (`bolao_id`),
  KEY `cambista_id` (`cambista_id`),
  KEY `status` (`status`),
  CONSTRAINT `bilhetes_ibfk_1` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`) ON DELETE CASCADE,
  CONSTRAINT `bilhetes_ibfk_2` FOREIGN KEY (`bolao_id`) REFERENCES `boloes` (`id`) ON DELETE SET NULL,
  CONSTRAINT `bilhetes_ibfk_3` FOREIGN KEY (`cambista_id`) REFERENCES `usuarios` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABELA: apostas
-- =====================================================

DROP TABLE IF EXISTS `apostas`;
CREATE TABLE `apostas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bilhete_id` int(11) NOT NULL,
  `jogo_id` int(11) NOT NULL,
  `usuario_id` int(11) NOT NULL,
  `palpite` enum('casa','empate','fora') NOT NULL,
  `valor_aposta` decimal(10,2) NOT NULL DEFAULT 1.00,
  `odd` decimal(8,2) DEFAULT 1.00,
  `status` enum('pendente','ganhou','perdeu','cancelada') DEFAULT 'pendente',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `bilhete_jogo_unique` (`bilhete_id`,`jogo_id`),
  KEY `jogo_id` (`jogo_id`),
  KEY `usuario_id` (`usuario_id`),
  CONSTRAINT `apostas_ibfk_1` FOREIGN KEY (`bilhete_id`) REFERENCES `bilhetes` (`id`) ON DELETE CASCADE,
  CONSTRAINT `apostas_ibfk_2` FOREIGN KEY (`jogo_id`) REFERENCES `jogos` (`id`) ON DELETE CASCADE,
  CONSTRAINT `apostas_ibfk_3` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABELA: pagamentos
-- =====================================================

DROP TABLE IF EXISTS `pagamentos`;
CREATE TABLE `pagamentos` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bilhete_id` int(11) NOT NULL,
  `usuario_id` int(11) NOT NULL,
  `valor` decimal(10,2) NOT NULL,
  `metodo` enum('pix','cartao','dinheiro','transferencia') DEFAULT 'pix',
  `status` enum('pendente','aprovado','rejeitado','cancelado') DEFAULT 'pendente',
  `transaction_id` varchar(255) DEFAULT NULL,
  `gateway_response` json DEFAULT NULL,
  `data_pagamento` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `bilhete_id` (`bilhete_id`),
  KEY `usuario_id` (`usuario_id`),
  KEY `transaction_id` (`transaction_id`),
  CONSTRAINT `pagamentos_ibfk_1` FOREIGN KEY (`bilhete_id`) REFERENCES `bilhetes` (`id`) ON DELETE CASCADE,
  CONSTRAINT `pagamentos_ibfk_2` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABELA: webhook_logs
-- =====================================================

DROP TABLE IF EXISTS `webhook_logs`;
CREATE TABLE `webhook_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transaction_id` varchar(255) DEFAULT NULL,
  `order_id` varchar(255) DEFAULT NULL,
  `status` varchar(50) DEFAULT NULL,
  `webhook_data` json DEFAULT NULL,
  `response_data` json DEFAULT NULL,
  `processed_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `success` tinyint(1) DEFAULT 0,
  `error_message` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `transaction_id` (`transaction_id`),
  KEY `order_id` (`order_id`),
  KEY `processed_at` (`processed_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABELA: comissoes
-- =====================================================

DROP TABLE IF EXISTS `comissoes`;
CREATE TABLE `comissoes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `usuario_id` int(11) NOT NULL,
  `afiliado_id` int(11) NOT NULL,
  `bilhete_id` int(11) NOT NULL,
  `valor_bilhete` decimal(10,2) NOT NULL,
  `percentual` decimal(5,2) NOT NULL,
  `valor_comissao` decimal(10,2) NOT NULL,
  `status` enum('pendente','pago','cancelado') DEFAULT 'pendente',
  `data_pagamento` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `usuario_id` (`usuario_id`),
  KEY `afiliado_id` (`afiliado_id`),
  KEY `bilhete_id` (`bilhete_id`),
  CONSTRAINT `comissoes_ibfk_1` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`) ON DELETE CASCADE,
  CONSTRAINT `comissoes_ibfk_2` FOREIGN KEY (`afiliado_id`) REFERENCES `usuarios` (`id`) ON DELETE CASCADE,
  CONSTRAINT `comissoes_ibfk_3` FOREIGN KEY (`bilhete_id`) REFERENCES `bilhetes` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- DADOS INICIAIS
-- =====================================================
