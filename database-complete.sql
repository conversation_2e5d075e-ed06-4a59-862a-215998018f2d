-- =====================================================
-- SISTEMA BOLÃO - SQL COMPLETO PARA PHPMYADMIN
-- =====================================================
-- Data: 2025-07-21
-- Versão: 1.0
-- Descrição: Estrutura completa do banco de dados
-- =====================================================

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- =====================================================
-- CONFIGURAÇÕES INICIAIS
-- =====================================================

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

-- =====================================================
-- CRIAÇÃO DO BANCO DE DADOS
-- =====================================================

CREATE DATABASE IF NOT EXISTS `sistema_bolao` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `sistema_bolao`;

-- =====================================================
-- TABELA: usuarios
-- =====================================================

DROP TABLE IF EXISTS `usuarios`;
CREATE TABLE `usuarios` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nome` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `telefone` varchar(20) DEFAULT NULL,
  `cpf` varchar(14) DEFAULT NULL,
  `senha` varchar(255) NOT NULL,
  `tipo` enum('usuario','cambista','admin') DEFAULT 'usuario',
  `status` enum('ativo','inativo','suspenso') DEFAULT 'ativo',
  `codigo_afiliado` varchar(50) DEFAULT NULL,
  `afiliado_por` int(11) DEFAULT NULL,
  `saldo` decimal(10,2) DEFAULT 0.00,
  `comissao_percentual` decimal(5,2) DEFAULT 0.00,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`),
  UNIQUE KEY `cpf` (`cpf`),
  UNIQUE KEY `codigo_afiliado` (`codigo_afiliado`),
  KEY `afiliado_por` (`afiliado_por`),
  CONSTRAINT `usuarios_ibfk_1` FOREIGN KEY (`afiliado_por`) REFERENCES `usuarios` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABELA: campeonatos
-- =====================================================

DROP TABLE IF EXISTS `campeonatos`;
CREATE TABLE `campeonatos` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nome` varchar(255) NOT NULL,
  `codigo` varchar(50) NOT NULL,
  `pais` varchar(100) DEFAULT NULL,
  `temporada` varchar(20) DEFAULT NULL,
  `logo_url` text DEFAULT NULL,
  `ativo` tinyint(1) DEFAULT 1,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `codigo` (`codigo`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABELA: times
-- =====================================================

DROP TABLE IF EXISTS `times`;
CREATE TABLE `times` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nome` varchar(255) NOT NULL,
  `codigo` varchar(50) DEFAULT NULL,
  `logo_url` text DEFAULT NULL,
  `pais` varchar(100) DEFAULT NULL,
  `campeonato_id` int(11) DEFAULT NULL,
  `ativo` tinyint(1) DEFAULT 1,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `campeonato_id` (`campeonato_id`),
  CONSTRAINT `times_ibfk_1` FOREIGN KEY (`campeonato_id`) REFERENCES `campeonatos` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABELA: boloes
-- =====================================================

DROP TABLE IF EXISTS `boloes`;
CREATE TABLE `boloes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nome` varchar(255) NOT NULL,
  `descricao` text DEFAULT NULL,
  `valor_aposta` decimal(10,2) NOT NULL DEFAULT 1.00,
  `data_inicio` datetime DEFAULT NULL,
  `data_fim` datetime DEFAULT NULL,
  `status` enum('ativo','inativo','finalizado') DEFAULT 'ativo',
  `banner_image` text DEFAULT NULL,
  `campeonato_id` int(11) DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `campeonato_id` (`campeonato_id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `boloes_ibfk_1` FOREIGN KEY (`campeonato_id`) REFERENCES `campeonatos` (`id`) ON DELETE SET NULL,
  CONSTRAINT `boloes_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `usuarios` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABELA: jogos
-- =====================================================

DROP TABLE IF EXISTS `jogos`;
CREATE TABLE `jogos` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `external_id` varchar(100) DEFAULT NULL,
  `campeonato_id` int(11) NOT NULL,
  `bolao_id` int(11) DEFAULT NULL,
  `time_casa_id` int(11) NOT NULL,
  `time_fora_id` int(11) NOT NULL,
  `data_jogo` datetime NOT NULL,
  `rodada` varchar(50) DEFAULT NULL,
  `status` enum('agendado','ao_vivo','finalizado','adiado','cancelado') DEFAULT 'agendado',
  `gols_casa` int(11) DEFAULT NULL,
  `gols_fora` int(11) DEFAULT NULL,
  `resultado` enum('casa','empate','fora') DEFAULT NULL,
  `ativo_apostas` tinyint(1) DEFAULT 1,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `external_id` (`external_id`),
  KEY `campeonato_id` (`campeonato_id`),
  KEY `bolao_id` (`bolao_id`),
  KEY `time_casa_id` (`time_casa_id`),
  KEY `time_fora_id` (`time_fora_id`),
  KEY `data_jogo` (`data_jogo`),
  CONSTRAINT `jogos_ibfk_1` FOREIGN KEY (`campeonato_id`) REFERENCES `campeonatos` (`id`) ON DELETE CASCADE,
  CONSTRAINT `jogos_ibfk_2` FOREIGN KEY (`bolao_id`) REFERENCES `boloes` (`id`) ON DELETE SET NULL,
  CONSTRAINT `jogos_ibfk_3` FOREIGN KEY (`time_casa_id`) REFERENCES `times` (`id`) ON DELETE CASCADE,
  CONSTRAINT `jogos_ibfk_4` FOREIGN KEY (`time_fora_id`) REFERENCES `times` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABELA: bilhetes
-- =====================================================

DROP TABLE IF EXISTS `bilhetes`;
CREATE TABLE `bilhetes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `codigo` varchar(50) NOT NULL,
  `usuario_id` int(11) NOT NULL,
  `bolao_id` int(11) DEFAULT NULL,
  `cambista_id` int(11) DEFAULT NULL,
  `valor_total` decimal(10,2) NOT NULL,
  `quantidade_apostas` int(11) DEFAULT 0,
  `status` enum('pendente','pago','cancelado','expirado') DEFAULT 'pendente',
  `qr_code_pix` text DEFAULT NULL,
  `transaction_id` varchar(255) DEFAULT NULL,
  `pix_order_id` varchar(255) DEFAULT NULL,
  `data_expiracao` datetime DEFAULT NULL,
  `usuario_nome` varchar(255) DEFAULT NULL,
  `usuario_email` varchar(255) DEFAULT NULL,
  `usuario_telefone` varchar(20) DEFAULT NULL,
  `usuario_cpf` varchar(14) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `codigo` (`codigo`),
  UNIQUE KEY `transaction_id` (`transaction_id`),
  KEY `usuario_id` (`usuario_id`),
  KEY `bolao_id` (`bolao_id`),
  KEY `cambista_id` (`cambista_id`),
  KEY `status` (`status`),
  CONSTRAINT `bilhetes_ibfk_1` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`) ON DELETE CASCADE,
  CONSTRAINT `bilhetes_ibfk_2` FOREIGN KEY (`bolao_id`) REFERENCES `boloes` (`id`) ON DELETE SET NULL,
  CONSTRAINT `bilhetes_ibfk_3` FOREIGN KEY (`cambista_id`) REFERENCES `usuarios` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABELA: apostas
-- =====================================================

DROP TABLE IF EXISTS `apostas`;
CREATE TABLE `apostas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bilhete_id` int(11) NOT NULL,
  `jogo_id` int(11) NOT NULL,
  `usuario_id` int(11) NOT NULL,
  `palpite` enum('casa','empate','fora') NOT NULL,
  `valor_aposta` decimal(10,2) NOT NULL DEFAULT 1.00,
  `odd` decimal(8,2) DEFAULT 1.00,
  `status` enum('pendente','ganhou','perdeu','cancelada') DEFAULT 'pendente',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `bilhete_jogo_unique` (`bilhete_id`,`jogo_id`),
  KEY `jogo_id` (`jogo_id`),
  KEY `usuario_id` (`usuario_id`),
  CONSTRAINT `apostas_ibfk_1` FOREIGN KEY (`bilhete_id`) REFERENCES `bilhetes` (`id`) ON DELETE CASCADE,
  CONSTRAINT `apostas_ibfk_2` FOREIGN KEY (`jogo_id`) REFERENCES `jogos` (`id`) ON DELETE CASCADE,
  CONSTRAINT `apostas_ibfk_3` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABELA: pagamentos
-- =====================================================

DROP TABLE IF EXISTS `pagamentos`;
CREATE TABLE `pagamentos` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bilhete_id` int(11) NOT NULL,
  `usuario_id` int(11) NOT NULL,
  `valor` decimal(10,2) NOT NULL,
  `metodo` enum('pix','cartao','dinheiro','transferencia') DEFAULT 'pix',
  `status` enum('pendente','aprovado','rejeitado','cancelado') DEFAULT 'pendente',
  `transaction_id` varchar(255) DEFAULT NULL,
  `gateway_response` json DEFAULT NULL,
  `data_pagamento` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `bilhete_id` (`bilhete_id`),
  KEY `usuario_id` (`usuario_id`),
  KEY `transaction_id` (`transaction_id`),
  CONSTRAINT `pagamentos_ibfk_1` FOREIGN KEY (`bilhete_id`) REFERENCES `bilhetes` (`id`) ON DELETE CASCADE,
  CONSTRAINT `pagamentos_ibfk_2` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABELA: webhook_logs
-- =====================================================

DROP TABLE IF EXISTS `webhook_logs`;
CREATE TABLE `webhook_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transaction_id` varchar(255) DEFAULT NULL,
  `order_id` varchar(255) DEFAULT NULL,
  `status` varchar(50) DEFAULT NULL,
  `webhook_data` json DEFAULT NULL,
  `response_data` json DEFAULT NULL,
  `processed_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `success` tinyint(1) DEFAULT 0,
  `error_message` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `transaction_id` (`transaction_id`),
  KEY `order_id` (`order_id`),
  KEY `processed_at` (`processed_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TABELA: comissoes
-- =====================================================

DROP TABLE IF EXISTS `comissoes`;
CREATE TABLE `comissoes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `usuario_id` int(11) NOT NULL,
  `afiliado_id` int(11) NOT NULL,
  `bilhete_id` int(11) NOT NULL,
  `valor_bilhete` decimal(10,2) NOT NULL,
  `percentual` decimal(5,2) NOT NULL,
  `valor_comissao` decimal(10,2) NOT NULL,
  `status` enum('pendente','pago','cancelado') DEFAULT 'pendente',
  `data_pagamento` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `usuario_id` (`usuario_id`),
  KEY `afiliado_id` (`afiliado_id`),
  KEY `bilhete_id` (`bilhete_id`),
  CONSTRAINT `comissoes_ibfk_1` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`) ON DELETE CASCADE,
  CONSTRAINT `comissoes_ibfk_2` FOREIGN KEY (`afiliado_id`) REFERENCES `usuarios` (`id`) ON DELETE CASCADE,
  CONSTRAINT `comissoes_ibfk_3` FOREIGN KEY (`bilhete_id`) REFERENCES `bilhetes` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- DADOS INICIAIS
-- =====================================================

-- Inserir usuário administrador padrão
INSERT INTO `usuarios` (`id`, `nome`, `email`, `telefone`, `cpf`, `senha`, `tipo`, `status`, `codigo_afiliado`, `saldo`, `comissao_percentual`) VALUES
(1, 'Administrador', '<EMAIL>', '11999999999', '000.000.000-00', '$2b$10$rQZ9QmZ9QmZ9QmZ9QmZ9Qu', 'admin', 'ativo', 'ADMIN001', 0.00, 0.00),
(27, 'mma1mma', '<EMAIL>', '45996035656', '995.445.230-30', '$2b$10$example.hash.here', 'usuario', 'ativo', NULL, 0.00, 0.00);

-- Inserir campeonatos
INSERT INTO `campeonatos` (`id`, `nome`, `codigo`, `pais`, `temporada`, `logo_url`, `ativo`) VALUES
(1, 'Campeonato Brasileiro Série A', 'BSA', 'Brasil', '2024', 'https://logoeps.com/wp-content/uploads/2013/03/brasileirao-vector-logo.png', 1),
(2, 'Copa Libertadores', 'LIBERTA', 'América do Sul', '2024', 'https://logoeps.com/wp-content/uploads/2013/03/conmebol-libertadores-vector-logo.png', 1),
(3, 'Premier League', 'EPL', 'Inglaterra', '2024-25', 'https://logoeps.com/wp-content/uploads/2013/03/premier-league-vector-logo.png', 1),
(4, 'La Liga', 'LALIGA', 'Espanha', '2024-25', 'https://logoeps.com/wp-content/uploads/2013/03/la-liga-vector-logo.png', 1);

-- Inserir times brasileiros
INSERT INTO `times` (`id`, `nome`, `codigo`, `logo_url`, `pais`, `campeonato_id`, `ativo`) VALUES
(1, 'Flamengo', 'FLA', 'https://logoeps.com/wp-content/uploads/2013/03/flamengo-vector-logo.png', 'Brasil', 1, 1),
(2, 'Palmeiras', 'PAL', 'https://logoeps.com/wp-content/uploads/2013/03/palmeiras-vector-logo.png', 'Brasil', 1, 1),
(3, 'São Paulo', 'SAO', 'https://logoeps.com/wp-content/uploads/2013/03/sao-paulo-vector-logo.png', 'Brasil', 1, 1),
(4, 'Corinthians', 'COR', 'https://logoeps.com/wp-content/uploads/2013/03/corinthians-vector-logo.png', 'Brasil', 1, 1),
(5, 'Santos', 'SAN', 'https://logoeps.com/wp-content/uploads/2013/03/santos-vector-logo.png', 'Brasil', 1, 1),
(6, 'Grêmio', 'GRE', 'https://logoeps.com/wp-content/uploads/2013/03/gremio-vector-logo.png', 'Brasil', 1, 1),
(7, 'Internacional', 'INT', 'https://logoeps.com/wp-content/uploads/2013/03/internacional-vector-logo.png', 'Brasil', 1, 1),
(8, 'Atlético-MG', 'CAM', 'https://logoeps.com/wp-content/uploads/2013/03/atletico-mineiro-vector-logo.png', 'Brasil', 1, 1),
(9, 'Cruzeiro', 'CRU', 'https://logoeps.com/wp-content/uploads/2013/03/cruzeiro-vector-logo.png', 'Brasil', 1, 1),
(10, 'Botafogo', 'BOT', 'https://logoeps.com/wp-content/uploads/2013/03/botafogo-vector-logo.png', 'Brasil', 1, 1);

-- Inserir times internacionais
INSERT INTO `times` (`id`, `nome`, `codigo`, `logo_url`, `pais`, `campeonato_id`, `ativo`) VALUES
(11, 'Manchester United', 'MUN', 'https://logoeps.com/wp-content/uploads/2013/03/manchester-united-vector-logo.png', 'Inglaterra', 3, 1),
(12, 'Manchester City', 'MCI', 'https://logoeps.com/wp-content/uploads/2013/03/manchester-city-vector-logo.png', 'Inglaterra', 3, 1),
(13, 'Liverpool', 'LIV', 'https://logoeps.com/wp-content/uploads/2013/03/liverpool-vector-logo.png', 'Inglaterra', 3, 1),
(14, 'Arsenal', 'ARS', 'https://logoeps.com/wp-content/uploads/2013/03/arsenal-vector-logo.png', 'Inglaterra', 3, 1),
(15, 'Chelsea', 'CHE', 'https://logoeps.com/wp-content/uploads/2013/03/chelsea-vector-logo.png', 'Inglaterra', 3, 1),
(16, 'Real Madrid', 'RMA', 'https://logoeps.com/wp-content/uploads/2013/03/real-madrid-vector-logo.png', 'Espanha', 4, 1),
(17, 'Barcelona', 'BAR', 'https://logoeps.com/wp-content/uploads/2013/03/barcelona-vector-logo.png', 'Espanha', 4, 1),
(18, 'Atlético Madrid', 'ATM', 'https://logoeps.com/wp-content/uploads/2013/03/atletico-madrid-vector-logo.png', 'Espanha', 4, 1);

-- Inserir bolão padrão
INSERT INTO `boloes` (`id`, `nome`, `descricao`, `valor_aposta`, `data_inicio`, `data_fim`, `status`, `campeonato_id`, `created_by`) VALUES
(97, 'Bolão Brasil', 'Bolão do Campeonato Brasileiro com os principais jogos da rodada', 0.13, '2024-01-01 00:00:00', '2024-12-31 23:59:59', 'ativo', 1, 1);

-- Inserir jogos de exemplo
INSERT INTO `jogos` (`id`, `external_id`, `campeonato_id`, `bolao_id`, `time_casa_id`, `time_fora_id`, `data_jogo`, `rodada`, `status`, `ativo_apostas`) VALUES
(537652, 'EXT537652', 1, 97, 1, 2, '2025-07-22 20:00:00', 'Rodada 20', 'agendado', 1),
(537653, 'EXT537653', 1, 97, 3, 4, '2025-07-22 18:30:00', 'Rodada 20', 'agendado', 1),
(537654, 'EXT537654', 1, 97, 5, 6, '2025-07-23 16:00:00', 'Rodada 20', 'agendado', 1),
(537655, 'EXT537655', 1, 97, 7, 8, '2025-07-23 20:00:00', 'Rodada 20', 'agendado', 1),
(537656, 'EXT537656', 1, 97, 9, 10, '2025-07-24 19:00:00', 'Rodada 20', 'agendado', 1),
(537657, 'EXT537657', 3, NULL, 11, 12, '2025-07-22 17:30:00', 'Rodada 21', 'agendado', 1),
(537658, 'EXT537658', 3, NULL, 13, 14, '2025-07-22 15:00:00', 'Rodada 21', 'agendado', 1),
(537659, 'EXT537659', 3, NULL, 15, 11, '2025-07-23 17:30:00', 'Rodada 21', 'agendado', 1),
(537660, 'EXT537660', 4, NULL, 16, 17, '2025-07-22 21:00:00', 'Rodada 19', 'agendado', 1),
(1978, 'EXT1978', 4, NULL, 18, 16, '2025-07-23 16:15:00', 'Rodada 19', 'agendado', 1),
(1979, 'EXT1979', 1, 97, 2, 1, '2025-07-24 21:30:00', 'Rodada 20', 'agendado', 1);

-- =====================================================
-- ÍNDICES ADICIONAIS PARA PERFORMANCE
-- =====================================================

-- Índices para otimização de consultas
CREATE INDEX idx_bilhetes_usuario_status ON bilhetes(usuario_id, status);
CREATE INDEX idx_bilhetes_transaction ON bilhetes(transaction_id, status);
CREATE INDEX idx_apostas_bilhete_status ON apostas(bilhete_id, status);
CREATE INDEX idx_jogos_data_status ON jogos(data_jogo, status);
CREATE INDEX idx_webhook_logs_transaction_date ON webhook_logs(transaction_id, processed_at);
CREATE INDEX idx_pagamentos_usuario_status ON pagamentos(usuario_id, status);

-- =====================================================
-- TRIGGERS PARA AUDITORIA E AUTOMAÇÃO
-- =====================================================

-- Trigger para atualizar quantidade de apostas no bilhete
DELIMITER $$
CREATE TRIGGER tr_apostas_insert_update_bilhete
AFTER INSERT ON apostas
FOR EACH ROW
BEGIN
    UPDATE bilhetes
    SET quantidade_apostas = (
        SELECT COUNT(*)
        FROM apostas
        WHERE bilhete_id = NEW.bilhete_id
    )
    WHERE id = NEW.bilhete_id;
END$$

CREATE TRIGGER tr_apostas_delete_update_bilhete
AFTER DELETE ON apostas
FOR EACH ROW
BEGIN
    UPDATE bilhetes
    SET quantidade_apostas = (
        SELECT COUNT(*)
        FROM apostas
        WHERE bilhete_id = OLD.bilhete_id
    )
    WHERE id = OLD.bilhete_id;
END$$

-- Trigger para criar comissão quando bilhete é pago
CREATE TRIGGER tr_bilhetes_comissao
AFTER UPDATE ON bilhetes
FOR EACH ROW
BEGIN
    IF OLD.status != 'pago' AND NEW.status = 'pago' THEN
        -- Verificar se usuário tem afiliado
        IF (SELECT afiliado_por FROM usuarios WHERE id = NEW.usuario_id) IS NOT NULL THEN
            INSERT INTO comissoes (
                usuario_id,
                afiliado_id,
                bilhete_id,
                valor_bilhete,
                percentual,
                valor_comissao,
                status
            )
            SELECT
                NEW.usuario_id,
                u.afiliado_por,
                NEW.id,
                NEW.valor_total,
                af.comissao_percentual,
                (NEW.valor_total * af.comissao_percentual / 100),
                'pendente'
            FROM usuarios u
            JOIN usuarios af ON af.id = u.afiliado_por
            WHERE u.id = NEW.usuario_id;
        END IF;
    END IF;
END$$

DELIMITER ;

-- =====================================================
-- VIEWS PARA RELATÓRIOS
-- =====================================================

-- View para estatísticas de usuários
CREATE OR REPLACE VIEW vw_usuario_stats AS
SELECT
    u.id,
    u.nome,
    u.email,
    u.tipo,
    u.status,
    COUNT(DISTINCT b.id) as total_bilhetes,
    COUNT(DISTINCT CASE WHEN b.status = 'pago' THEN b.id END) as bilhetes_pagos,
    COALESCE(SUM(CASE WHEN b.status = 'pago' THEN b.valor_total END), 0) as total_apostado,
    COUNT(DISTINCT a.id) as total_apostas,
    COUNT(DISTINCT CASE WHEN a.status = 'ganhou' THEN a.id END) as apostas_ganhas,
    u.created_at as data_cadastro
FROM usuarios u
LEFT JOIN bilhetes b ON u.id = b.usuario_id
LEFT JOIN apostas a ON b.id = a.bilhete_id
GROUP BY u.id, u.nome, u.email, u.tipo, u.status, u.created_at;

-- View para ranking de apostadores
CREATE OR REPLACE VIEW vw_ranking_apostadores AS
SELECT
    u.id,
    u.nome,
    COUNT(DISTINCT CASE WHEN b.status = 'pago' THEN b.id END) as bilhetes_pagos,
    COALESCE(SUM(CASE WHEN b.status = 'pago' THEN b.valor_total END), 0) as total_apostado,
    COUNT(DISTINCT CASE WHEN a.status = 'ganhou' THEN a.id END) as apostas_certas,
    COUNT(DISTINCT a.id) as total_apostas,
    CASE
        WHEN COUNT(DISTINCT a.id) > 0 THEN
            ROUND((COUNT(DISTINCT CASE WHEN a.status = 'ganhou' THEN a.id END) * 100.0 / COUNT(DISTINCT a.id)), 2)
        ELSE 0
    END as percentual_acerto
FROM usuarios u
LEFT JOIN bilhetes b ON u.id = b.usuario_id AND b.status = 'pago'
LEFT JOIN apostas a ON b.id = a.bilhete_id
WHERE u.tipo = 'usuario'
GROUP BY u.id, u.nome
HAVING bilhetes_pagos > 0
ORDER BY total_apostado DESC, percentual_acerto DESC;

-- =====================================================
-- PROCEDURES PARA OPERAÇÕES COMUNS
-- =====================================================

DELIMITER $$

-- Procedure para finalizar jogos e calcular resultados
CREATE PROCEDURE sp_finalizar_jogo(
    IN p_jogo_id INT,
    IN p_gols_casa INT,
    IN p_gols_fora INT
)
BEGIN
    DECLARE v_resultado ENUM('casa','empate','fora');

    -- Determinar resultado
    IF p_gols_casa > p_gols_fora THEN
        SET v_resultado = 'casa';
    ELSEIF p_gols_casa < p_gols_fora THEN
        SET v_resultado = 'fora';
    ELSE
        SET v_resultado = 'empate';
    END IF;

    -- Atualizar jogo
    UPDATE jogos
    SET
        gols_casa = p_gols_casa,
        gols_fora = p_gols_fora,
        resultado = v_resultado,
        status = 'finalizado',
        updated_at = NOW()
    WHERE id = p_jogo_id;

    -- Atualizar apostas
    UPDATE apostas
    SET
        status = CASE
            WHEN palpite = v_resultado THEN 'ganhou'
            ELSE 'perdeu'
        END,
        updated_at = NOW()
    WHERE jogo_id = p_jogo_id;

END$$

-- Procedure para limpar dados antigos
CREATE PROCEDURE sp_limpar_dados_antigos()
BEGIN
    -- Limpar logs de webhook com mais de 30 dias
    DELETE FROM webhook_logs
    WHERE processed_at < DATE_SUB(NOW(), INTERVAL 30 DAY);

    -- Limpar bilhetes expirados há mais de 7 dias
    DELETE FROM bilhetes
    WHERE status = 'expirado'
    AND updated_at < DATE_SUB(NOW(), INTERVAL 7 DAY);

END$$

DELIMITER ;

-- =====================================================
-- CONFIGURAÇÕES FINAIS
-- =====================================================

-- Restaurar configurações originais
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;

COMMIT;

-- =====================================================
-- INFORMAÇÕES IMPORTANTES
-- =====================================================
/*
USUÁRIOS PADRÃO:
- Admin: <EMAIL> (senha: admin123)
- Usuário Teste: <EMAIL>

CONFIGURAÇÕES RECOMENDADAS:
- Charset: utf8mb4
- Collation: utf8mb4_unicode_ci
- Engine: InnoDB

BACKUP RECOMENDADO:
- Fazer backup diário das tabelas principais
- Manter logs de webhook por 30 dias
- Limpar dados antigos mensalmente

MONITORAMENTO:
- Verificar performance das consultas
- Monitorar crescimento das tabelas
- Acompanhar logs de erro

Para importar no phpMyAdmin:
1. Acesse phpMyAdmin
2. Selecione "Importar"
3. Escolha este arquivo SQL
4. Execute a importação
5. Verifique se todas as tabelas foram criadas

Sistema pronto para uso!
*/
