import { type NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function GET() {
  try {
    console.log("🔄 Admin: Buscando dados reais...")

    // Buscar bolões reais
    const boloes = await executeQuery(`
      SELECT * FROM boloes
      ORDER BY id DESC
      LIMIT 50
    `)

    // Buscar estatísticas simples
    const statsAtivos = await executeQuery('SELECT COUNT(*) as count FROM boloes WHERE status = "ativo"')

    const stats = {
      ativos: statsAtivos[0]?.count || 0,
      participantes: 0,
      faturamento: 0,
      finalizandoHoje: 0
    }

    console.log(`✅ Admin: ${boloes.length} bolões carregados`)

    return NextResponse.json({
      success: true,
      boloes: boloes || [],
      stats: stats,
      source: 'database'
    })
  } catch (error) {
    console.error("Erro ao buscar bolões:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Erro interno do servidor",
        message: (error as Error).message || "Erro desconhecido",
        boloes: [],
        stats: { ativos: 0, participantes: 0, faturamento: 0, finalizandoHoje: 0 },
      },
      { status: 500 },
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log("🚀 Iniciando criação de bolão...")

    const data = await request.json()
    console.log("📋 Dados recebidos:", data)

    // Validação dos campos obrigatórios
    if (!data.nome || !data.valor_aposta || !data.premio_total || !data.data_inicio || !data.data_fim) {
      console.error("❌ Campos obrigatórios faltando")
      return NextResponse.json(
        {
          success: false,
          error: "Campos obrigatórios: nome, valor_aposta, premio_total, data_inicio, data_fim",
        },
        { status: 400 }
      )
    }

    // Buscar um usuário admin para usar como criador
    console.log("👤 Buscando usuário admin...")
    const adminUsers = await executeQuery("SELECT id FROM usuarios WHERE tipo = 'admin' LIMIT 1")

    let criadoPor = null
    if (adminUsers && adminUsers.length > 0) {
      criadoPor = adminUsers[0].id
      console.log("✅ Admin encontrado:", criadoPor)
    } else {
      console.log("⚠️ Nenhum admin encontrado, criando sem criado_por")
    }

    // Criar bolão real no banco de dados
    console.log("💾 Salvando bolão no banco...")

    const result = await executeQuery(`
      INSERT INTO boloes (
        nome, descricao, valor_aposta, premio_total, max_participantes,
        data_inicio, data_fim, status, banner_image, criado_por, data_criacao
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
    `, [
      data.nome,
      data.descricao || '',
      parseFloat(data.valor_aposta),
      parseFloat(data.premio_total),
      data.max_participantes || 100,
      data.data_inicio,
      data.data_fim,
      'ativo',
      data.banner_image || null,
      criadoPor
    ])

    const bolaoId = result.insertId
    console.log("✅ Bolão criado com ID:", bolaoId)

    // Sincronizar campeonatos e partidas automaticamente
    if (data.campeonatos_selecionados && data.campeonatos_selecionados.length > 0) {
      console.log("🔄 Sincronizando campeonatos e partidas...")

      try {
        // Salvar campeonatos selecionados
        await executeQuery(`
          UPDATE boloes SET campeonatos_selecionados = ? WHERE id = ?
        `, [JSON.stringify(data.campeonatos_selecionados), bolaoId])

        console.log("✅ Campeonatos sincronizados")
      } catch (syncError) {
        console.warn("⚠️ Erro na sincronização:", (syncError as Error).message)
      }
    }

    return NextResponse.json({
      success: true,
      message: "Bolão criado com sucesso",
      bolao: {
        id: bolaoId,
        nome: data.nome,
        valor_aposta: data.valor_aposta,
        premio_total: data.premio_total,
        status: 'ativo'
      }
    })

  } catch (error) {
    console.error("❌ Erro ao criar bolão:", (error as Error).message)
    return NextResponse.json(
      {
        success: false,
        error: "Erro interno do servidor",
        message: (error as Error).message
      },
      { status: 500 }
    )
  }
}
