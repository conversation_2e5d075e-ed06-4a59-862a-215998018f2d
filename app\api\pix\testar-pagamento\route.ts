import { NextRequest, NextResponse } from "next/server"
import { executeQuery, initializeDatabase } from "@/lib/database-config"

const PIX_API_TOKEN = process.env.PIX_API_TOKEN || 'Gk0e0W5bc2Guozw97NXv8duZbZCDbsedwMuTQNqqn5ZMT549Z0qDDZBF5ZbG9sHhXw9egbly3eiFJ4PKfs6ur+zzXs30gSDV6CYl8sQmSPJaISqpqf/FtJ2k30O3kO9ZsNwasalD0PGpD2wlAbb4ynO5S07P1kgWZRtw4w=='

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = request.nextUrl
    const bilhete_codigo = searchParams.get('bilhete') || 'BLT1752907418177249YOW51' // Seu bilhete

    console.log(`🔍 Testando pagamento do bilhete: ${bilhete_codigo}`)

    await initializeDatabase()

    // Buscar dados do bilhete no banco
    const bilhetes = await executeQuery(`
      SELECT id, codigo, transaction_id, pix_order_id, status, valor_total, created_at
      FROM bilhetes 
      WHERE codigo = ?
      LIMIT 1
    `, [bilhete_codigo])

    if (bilhetes.length === 0) {
      return NextResponse.json({
        error: `Bilhete ${bilhete_codigo} não encontrado no banco de dados`
      }, { status: 404 })
    }

    const bilhete = bilhetes[0]
    console.log('📋 Dados do bilhete:', bilhete)

    const resultado = {
      bilhete_info: {
        codigo: bilhete.codigo,
        transaction_id: bilhete.transaction_id,
        pix_order_id: bilhete.pix_order_id,
        status_atual: bilhete.status,
        valor: parseFloat(bilhete.valor_total),
        criado_em: bilhete.created_at
      },
      testes_realizados: [],
      status_final: 'PENDENTE'
    }

    // Teste 1: Consultar por transaction_id na API que funciona
    if (bilhete.transaction_id) {
      try {
        console.log(`🔍 Testando com transaction_id: ${bilhete.transaction_id}`)
        
        const response = await fetch('https://api.meiodepagamento.com/api/V1/ConsultarTransacao', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            token: PIX_API_TOKEN,
            transaction_id: bilhete.transaction_id
          })
        })

        const teste1 = {
          metodo: 'ConsultarTransacao por transaction_id',
          url: 'https://api.meiodepagamento.com/api/V1/ConsultarTransacao',
          status_code: response.status,
          sucesso: response.ok
        }

        if (response.ok) {
          const data = await response.json()
          teste1.resposta = data
          teste1.status_pagamento = data.status
          
          if (data.status === 'PAID' || data.status === 'APPROVED') {
            resultado.status_final = 'PAGO'
            
            // Atualizar no banco de dados
            await executeQuery(`
              UPDATE bilhetes 
              SET status = 'pago', updated_at = NOW() 
              WHERE id = ?
            `, [bilhete.id])
            
            teste1.acao = 'Bilhete atualizado para PAGO no banco de dados'
          }
        } else {
          teste1.erro = await response.text()
        }

        resultado.testes_realizados.push(teste1)
      } catch (error) {
        resultado.testes_realizados.push({
          metodo: 'ConsultarTransacao por transaction_id',
          erro: error instanceof Error ? error.message : String(error)
        })
      }
    }

    // Teste 2: Consultar por order_id se disponível
    if (bilhete.pix_order_id) {
      try {
        console.log(`🔍 Testando com pix_order_id: ${bilhete.pix_order_id}`)
        
        const response = await fetch('https://api.meiodepagamento.com/api/V1/ConsultarTransacao', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            token: PIX_API_TOKEN,
            order_id: bilhete.pix_order_id
          })
        })

        const teste2 = {
          metodo: 'ConsultarTransacao por order_id',
          url: 'https://api.meiodepagamento.com/api/V1/ConsultarTransacao',
          status_code: response.status,
          sucesso: response.ok
        }

        if (response.ok) {
          const data = await response.json()
          teste2.resposta = data
          teste2.status_pagamento = data.status
          
          if (data.status === 'PAID' || data.status === 'APPROVED') {
            resultado.status_final = 'PAGO'
            
            // Atualizar no banco de dados se ainda não foi
            if (bilhete.status === 'pendente') {
              await executeQuery(`
                UPDATE bilhetes 
                SET status = 'pago', updated_at = NOW() 
                WHERE id = ?
              `, [bilhete.id])
              
              teste2.acao = 'Bilhete atualizado para PAGO no banco de dados'
            }
          }
        } else {
          teste2.erro = await response.text()
        }

        resultado.testes_realizados.push(teste2)
      } catch (error) {
        resultado.testes_realizados.push({
          metodo: 'ConsultarTransacao por order_id',
          erro: error instanceof Error ? error.message : String(error)
        })
      }
    }

    // Verificar status final no banco
    const bilheteAtualizado = await executeQuery(`
      SELECT status FROM bilhetes WHERE id = ?
    `, [bilhete.id])

    if (bilheteAtualizado.length > 0) {
      resultado.status_banco_atual = bilheteAtualizado[0].status
    }

    return NextResponse.json({
      success: true,
      message: `Teste de pagamento para bilhete ${bilhete_codigo}`,
      resultado,
      instrucoes: {
        como_usar: [
          `GET /api/pix/testar-pagamento?bilhete=${bilhete_codigo}`,
          'Este endpoint testa se o pagamento foi processado',
          'Se encontrar pagamento aprovado, atualiza automaticamente o status'
        ],
        proximos_passos: resultado.status_final === 'PAGO' ? [
          '✅ Pagamento confirmado!',
          '✅ Status do bilhete atualizado',
          '🎉 Você pode verificar na aplicação'
        ] : [
          '⏳ Pagamento ainda não processado',
          '🔄 Tente novamente em alguns minutos',
          '📞 Contate o suporte se o problema persistir'
        ]
      }
    })

  } catch (error) {
    console.error('❌ Erro no teste de pagamento:', error)
    return NextResponse.json({
      error: 'Erro ao testar pagamento',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}
