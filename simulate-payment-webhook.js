#!/usr/bin/env node

// Script para simular webhook de pagamento PIX
import fetch from 'node-fetch';

async function simulatePaymentWebhook(bilheteCodigo) {
  console.log(`🎯 Simulando pagamento para bilhete: ${bilheteCodigo}`);
  
  try {
    // Simular webhook local (como se fosse o servidor de pagamento chamando)
    const response = await fetch('http://localhost:3000/api/v1/MP/webhookruntransation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'PIX-Payment-Gateway/1.0',
        'X-Webhook-Source': 'payment-provider'
      },
      body: JSON.stringify({
        order_id: bilheteCodigo,
        status: "PAID",
        type: "PIXOUT",
        message: "Payment approved via PIX",
        timestamp: new Date().toISOString(),
        payment_method: "PIX",
        amount: 0.13
      })
    });

    const result = await response.json();
    
    if (response.ok && result.processed_successfully) {
      console.log('✅ Pagamento processado com sucesso!');
      console.log(`📊 Bilhete: ${result.bilhete_codigo}`);
      console.log(`💰 Valor: R$ ${result.valor}`);
      console.log(`📅 Timestamp: ${result.timestamp}`);
      
      // Verificar se o status foi atualizado
      console.log('\n🔍 Verificando status atualizado...');
      const checkResponse = await fetch(`http://localhost:3000/api/debug-bilhete?codigo=${bilheteCodigo}`);
      const checkResult = await checkResponse.json();
      
      if (checkResult.success && checkResult.bilhetes.length > 0) {
        const bilhete = checkResult.bilhetes[0];
        console.log(`✅ Status confirmado: ${bilhete.status}`);
        console.log(`📝 Última atualização: ${bilhete.updated_at}`);
        
        if (bilhete.status === 'pago') {
          console.log('\n🎉 PAGAMENTO CONFIRMADO COM SUCESSO! 🎉');
          return true;
        }
      }
    } else {
      console.log('❌ Erro ao processar pagamento:');
      console.log(JSON.stringify(result, null, 2));
      return false;
    }
    
  } catch (error) {
    console.error('❌ Erro na simulação:', error.message);
    return false;
  }
}

// Função para buscar bilhetes pendentes
async function findPendingBilhetes() {
  try {
    const response = await fetch('http://localhost:3000/api/debug-bilhete');
    const result = await response.json();
    
    if (result.success && result.bilhetes.length > 0) {
      const pendentes = result.bilhetes.filter(b => b.status === 'pendente');
      return pendentes;
    }
    return [];
  } catch (error) {
    console.error('❌ Erro ao buscar bilhetes:', error.message);
    return [];
  }
}

// Função principal
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length > 0) {
    // Simular pagamento para bilhete específico
    const bilheteCodigo = args[0];
    console.log(`🎯 Simulando pagamento para bilhete específico: ${bilheteCodigo}\n`);
    await simulatePaymentWebhook(bilheteCodigo);
  } else {
    // Buscar bilhetes pendentes e simular pagamento
    console.log('🔍 Buscando bilhetes pendentes...\n');
    const pendentes = await findPendingBilhetes();
    
    if (pendentes.length === 0) {
      console.log('ℹ️ Nenhum bilhete pendente encontrado.');
      console.log('\n💡 Para simular pagamento de um bilhete específico:');
      console.log('   node simulate-payment-webhook.js BLT175312691185427EZFRF1');
      return;
    }
    
    console.log(`📊 Encontrados ${pendentes.length} bilhetes pendentes:`);
    pendentes.forEach((bilhete, index) => {
      console.log(`   ${index + 1}. ${bilhete.codigo} - R$ ${bilhete.valor} (${bilhete.usuario_nome})`);
    });
    
    console.log('\n🎯 Simulando pagamento para todos os bilhetes pendentes...\n');
    
    for (const bilhete of pendentes) {
      console.log(`\n${'='.repeat(50)}`);
      const success = await simulatePaymentWebhook(bilhete.codigo);
      if (success) {
        console.log(`✅ ${bilhete.codigo} - PAGO`);
      } else {
        console.log(`❌ ${bilhete.codigo} - ERRO`);
      }
      
      // Aguardar um pouco entre os pagamentos
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log(`\n${'='.repeat(50)}`);
    console.log('🎉 Simulação concluída!');
  }
}

// Executar
main().catch(console.error);
