import { NextRequest, NextResponse } from "next/server"
import { executeQuery } from "@/lib/database-config"

// Sistema de monitoramento ativo para pagamentos reais PIX
export async function POST(request: NextRequest) {
  try {
    console.log("🔍 Iniciando monitoramento de pagamentos reais...")

    // Buscar bilhetes pendentes dos últimos 30 minutos
    const bilhetesPendentes = await executeQuery(`
      SELECT 
        id,
        codigo,
        transaction_id,
        pix_order_id,
        valor_total,
        created_at,
        usuario_id,
        status
      FROM bilhetes 
      WHERE status = 'pendente' 
      AND created_at >= DATE_SUB(NOW(), INTERVAL 30 MINUTE)
      ORDER BY created_at DESC
      LIMIT 10
    `)

    console.log(`📊 Encontrados ${bilhetesPendentes.length} bilhetes pendentes para verificar`)

    if (bilhetesPendentes.length === 0) {
      return NextResponse.json({
        success: true,
        message: "Nenhum bilhete pendente encontrado",
        checked: 0,
        processed: 0,
        timestamp: new Date().toISOString()
      })
    }

    let bilhetesProcessados = 0
    const resultados = []

    // Verificar cada bilhete pendente na API PIX real
    for (const bilhete of bilhetesPendentes) {
      try {
        console.log(`🔍 Verificando pagamento real para: ${bilhete.codigo}`)

        // Consultar status na API PIX real
        const statusPagamento = await consultarStatusPIXReal(bilhete.transaction_id)

        if (statusPagamento.pago) {
          console.log(`💰 Pagamento REAL confirmado para bilhete: ${bilhete.codigo}`)

          // Disparar webhook automaticamente
          const webhookResult = await dispararWebhookPagamentoReal(bilhete, request.nextUrl.origin)

          if (webhookResult.success) {
            bilhetesProcessados++
            resultados.push({
              bilhete_codigo: bilhete.codigo,
              transaction_id: bilhete.transaction_id,
              status: 'pagamento_real_confirmado',
              webhook_response: webhookResult,
              valor: parseFloat(bilhete.valor_total),
              confirmado_em: new Date().toISOString()
            })
          } else {
            resultados.push({
              bilhete_codigo: bilhete.codigo,
              transaction_id: bilhete.transaction_id,
              status: 'erro_webhook',
              error: webhookResult.error
            })
          }
        } else {
          resultados.push({
            bilhete_codigo: bilhete.codigo,
            transaction_id: bilhete.transaction_id,
            status: 'ainda_pendente',
            api_response: statusPagamento,
            verificado_em: new Date().toISOString()
          })
        }

      } catch (error) {
        console.error(`❌ Erro ao verificar bilhete ${bilhete.codigo}:`, error)
        resultados.push({
          bilhete_codigo: bilhete.codigo,
          transaction_id: bilhete.transaction_id,
          status: 'erro_verificacao',
          error: error instanceof Error ? error.message : String(error)
        })
      }
    }

    return NextResponse.json({
      success: true,
      message: "Monitoramento de pagamentos reais concluído",
      timestamp: new Date().toISOString(),
      estatisticas: {
        bilhetes_verificados: bilhetesPendentes.length,
        pagamentos_reais_detectados: bilhetesProcessados,
        webhooks_disparados: bilhetesProcessados
      },
      resultados,
      proxima_verificacao: "Execute novamente em 1-2 minutos"
    })

  } catch (error) {
    console.error("❌ Erro no monitoramento de pagamentos reais:", error)
    
    return NextResponse.json({
      error: "Erro no monitoramento de pagamentos reais",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}

// Função para consultar status real na API PIX
async function consultarStatusPIXReal(transactionId: string) {
  try {
    const PIX_API_URL = process.env.PIX_API_URL || 'https://api.meiodepagamento.com/api/V1'
    const PIX_TOKEN = process.env.PIX_API_TOKEN

    if (!PIX_TOKEN) {
      console.log("⚠️ Token PIX não configurado")
      return { pago: false, erro: 'Token não configurado' }
    }

    console.log(`📡 Consultando API PIX real para: ${transactionId}`)

    // Tentar consultar a API real
    const response = await fetch(`${PIX_API_URL}/ConsultarTransacao`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${PIX_TOKEN}`
      },
      body: JSON.stringify({
        token: PIX_TOKEN,
        transaction_id: transactionId
      }),
      signal: AbortSignal.timeout(15000) // 15 segundos timeout
    })

    if (response.ok) {
      const data = await response.json()
      console.log(`📋 Resposta da API PIX para ${transactionId}:`, data)
      
      // Verificar se o status indica pagamento confirmado
      const statusPago = data.status === 'PAID' || 
                        data.status === 'paid' || 
                        data.status === 'APROVADO' ||
                        data.status === 'aprovado' ||
                        data.payment_status === 'PAID' ||
                        data.payment_status === 'paid'

      return { 
        pago: statusPago, 
        status_original: data.status,
        payment_status: data.payment_status,
        data_resposta: data,
        consultado_em: new Date().toISOString()
      }
    } else {
      console.log(`⚠️ API PIX retornou erro ${response.status} para ${transactionId}`)
      const errorText = await response.text()
      return { 
        pago: false, 
        erro_api: response.status,
        erro_detalhes: errorText
      }
    }

  } catch (error) {
    console.error("❌ Erro na consulta à API PIX:", error)
    return { 
      pago: false, 
      erro: error instanceof Error ? error.message : String(error)
    }
  }
}

// Função para disparar webhook quando pagamento real for confirmado
async function dispararWebhookPagamentoReal(bilhete: any, baseUrl: string) {
  try {
    const webhookPayload = {
      qr_code_payment_id: bilhete.transaction_id,
      transaction_id: bilhete.transaction_id,
      order_id: bilhete.transaction_id,
      amount: parseFloat(bilhete.valor_total).toString(),
      description: `Bolão - ${bilhete.codigo}`,
      status: "PAID",
      end_to_end_id: `E${Date.now()}`,
      last_updated_at: new Date().toISOString(),
      error: null,
      source: "real_payment_monitor"
    }

    const webhookUrl = `${baseUrl}/api/v1/MP/webhookruntransation`
    
    console.log(`📤 Disparando webhook para pagamento real: ${bilhete.codigo}`)
    
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Sistema-Bolao-Real-Payment-Monitor/1.0'
      },
      body: JSON.stringify(webhookPayload),
      signal: AbortSignal.timeout(15000) // 15 segundos timeout
    })

    const result = await response.json()

    return {
      success: response.ok,
      status: response.status,
      payload: webhookPayload,
      response: result
    }

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    }
  }
}

// Método GET para status do monitoramento
export async function GET(request: NextRequest) {
  try {
    // Buscar bilhetes pendentes
    const bilhetesPendentes = await executeQuery(`
      SELECT COUNT(*) as count FROM bilhetes
      WHERE status = 'pendente' 
      AND created_at >= DATE_SUB(NOW(), INTERVAL 30 MINUTE)
    `)

    return NextResponse.json({
      message: "Sistema de Monitoramento de Pagamentos Reais PIX",
      status: "ativo",
      timestamp: new Date().toISOString(),
      bilhetes_pendentes: bilhetesPendentes[0]?.count || 0,
      configuracao: {
        intervalo_recomendado: "1-2 minutos",
        tempo_limite: "30 minutos",
        timeout_api: "15 segundos",
        api_pix_url: process.env.PIX_API_URL,
        token_configurado: !!process.env.PIX_API_TOKEN
      },
      como_usar: {
        executar_monitoramento: "POST /api/monitor-real-payments",
        automatizar: "Configure um cron job para executar a cada 1-2 minutos",
        comando_cron: "*/2 * * * * curl -X POST https://ouroemu.site/api/monitor-real-payments"
      },
      instrucoes: [
        "1. Execute POST /api/monitor-real-payments para verificar pagamentos reais",
        "2. O sistema consulta a API PIX para cada bilhete pendente",
        "3. Pagamentos confirmados disparam webhook automaticamente",
        "4. Modal aparece automaticamente quando pagamento é detectado",
        "5. Configure cron job para automatizar completamente"
      ]
    })

  } catch (error) {
    return NextResponse.json({
      error: "Erro ao verificar status",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}
