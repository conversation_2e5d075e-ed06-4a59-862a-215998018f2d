import { type NextRequest, NextResponse } from "next/server"
import { executeQuery } from "@/lib/database-config"
import bcrypt from 'bcryptjs'

// Força renderização dinâmica para evitar erro de build estático
export const dynamic = 'force-dynamic'

export async function POST(request: NextRequest) {
  try {


    const body = await request.json()
    const { nome, email, telefone, cpf, senha, affiliate_code } = body

    console.log("📝 Registrando usuário:", { nome, email, telefone, cpf, affiliate_code })

    // Validações básicas
    if (!nome || !email || !senha) {
      return NextResponse.json(
        { error: "Nome, email e senha são obrigatórios" },
        { status: 400 }
      )
    }

    if (!email.includes("@")) {
      return NextResponse.json(
        { error: "Email inválido" },
        { status: 400 }
      )
    }

    if (senha.length < 6) {
      return NextResponse.json(
        { error: "Senha deve ter pelo menos 6 caracteres" },
        { status: 400 }
      )
    }

    // Verificar se email já existe
    const existingUsers = await executeQuery(
      "SELECT id FROM usuarios WHERE email = ?",
      [email]
    )

    if (existingUsers.length > 0) {
      return NextResponse.json(
        { error: "Email já cadastrado" },
        { status: 409 }
      )
    }

    // Hash da senha
    const senhaHash = await bcrypt.hash(senha, 10)

    let afiliadoPor = null

    // Se há código de afiliado, verificar se é válido
    if (affiliate_code) {
      const afiliados = await executeQuery(
        "SELECT id FROM usuarios WHERE codigo_afiliado = ? AND tipo IN ('cambista', 'admin') AND status = 'ativo'",
        [affiliate_code]
      )

      if (afiliados.length > 0) {
        afiliadoPor = afiliados[0].id
        console.log("✅ Código de afiliado válido:", affiliate_code)
      } else {
        console.log("⚠️ Código de afiliado inválido:", affiliate_code)
      }
    }

    // Inserir usuário
    const result = await executeQuery(
      `INSERT INTO usuarios (nome, email, telefone, cpf, senha, tipo, afiliado_por, status)
       VALUES (?, ?, ?, ?, ?, 'usuario', ?, 'ativo')`,
      [nome, email, telefone, cpf, senhaHash, afiliadoPor]
    )

    const userId = (result as any).insertId

    // Se foi indicado por afiliado, registrar na tabela de comissões
    if (afiliadoPor) {
      try {
        // Buscar dados do afiliado
        const afiliados = await executeQuery(
          "SELECT * FROM usuarios WHERE id = ?",
          [afiliadoPor]
        )

        if (afiliados.length > 0) {
          const afiliado = afiliados[0]

          // Calcular comissão padrão (5% sobre valor base de R$ 25)
          const valorBase = 25.00
          const percentualComissao = parseFloat(afiliado.comissao_percentual || 5)
          const valorComissao = (valorBase * percentualComissao) / 100

          console.log("✅ Indicação registrada:", {
            afiliado: afiliado.nome,
            usuario: nome,
            comissao_percentual: percentualComissao
          })
        }
      } catch (error) {
        console.error("❌ Erro ao processar indicação:", error)
        // Não falhar o registro se houver erro na indicação
      }
    }

    // Retornar dados do usuário (sem senha)
    const userData = {
      id: userId,
      nome,
      email,
      telefone,
      cpf,
      tipo: 'usuario',
      afiliado_por: afiliadoPor
    }

    console.log("✅ Usuário registrado com sucesso:", userData)

    return NextResponse.json({
      success: true,
      message: "Usuário registrado com sucesso",
      user: userData,
      is_affiliate_referral: !!afiliadoPor
    })

  } catch (error: any) {
    console.error("❌ Erro ao registrar usuário:", error)
    
    return NextResponse.json(
      { 
        error: "Erro interno do servidor",
        message: "Não foi possível registrar o usuário"
      },
      { status: 500 }
    )
  }
}
