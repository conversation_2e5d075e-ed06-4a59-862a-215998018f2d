import { NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

// Força notificação de pagamento para teste da interface
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { bilhete_codigo, user_id } = body

    console.log("🔔 Forçando notificação de pagamento:", { bilhete_codigo, user_id })

    await initializeDatabase()

    // Buscar o bilhete
    const bilhetes = await executeQuery(`
      SELECT id, codigo, transaction_id, status, valor_total, usuario_id
      FROM bilhetes 
      WHERE codigo = ? OR transaction_id = ?
      LIMIT 1
    `, [bilhete_codigo, bilhete_codigo])

    if (bilhetes.length === 0) {
      return NextResponse.json({
        success: false,
        message: "Bilhete não encontrado",
        bilhete_codigo
      }, { status: 404 })
    }

    const bilhete = bilhetes[0]
    console.log("🎫 Bilhete encontrado:", bilhete)

    // Simular que o pagamento foi detectado
    const notificationData = {
      type: 'payment_confirmed',
      bilhete: {
        codigo: bilhete.codigo,
        valor: parseFloat(bilhete.valor_total),
        status: bilhete.status,
        transaction_id: bilhete.transaction_id,
        usuario_id: bilhete.usuario_id
      },
      timestamp: new Date().toISOString(),
      message: 'Pagamento confirmado via notificação forçada'
    }

    // Retornar dados para que o frontend possa processar
    return NextResponse.json({
      success: true,
      message: "Notificação de pagamento enviada",
      notification: notificationData,
      instructions: [
        "1. Esta API simula uma notificação de pagamento",
        "2. O frontend deve escutar este evento",
        "3. Quando receber, deve mostrar o modal de sucesso",
        "4. Use esta API para testar a interface"
      ],
      frontend_action: {
        event_name: 'pixPaymentConfirmed',
        event_detail: {
          codigo: bilhete.codigo,
          valor: parseFloat(bilhete.valor_total),
          data: new Date().toLocaleString('pt-BR'),
          transactionId: bilhete.transaction_id || bilhete.codigo,
          timestamp: Date.now()
        }
      }
    })

  } catch (error: any) {
    console.error('❌ Erro ao forçar notificação:', error)
    return NextResponse.json({
      success: false,
      message: 'Erro ao forçar notificação',
      error: error.message
    }, { status: 500 })
  }
}

// Método GET para instruções
export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: "API de Notificação Forçada de Pagamento",
    description: "Esta API força uma notificação de pagamento para testar a interface",
    usage: {
      method: "POST",
      endpoint: "/api/force-payment-notification",
      body: {
        bilhete_codigo: "BLT175312844531027L5H1MB",
        user_id: 27
      }
    },
    instructions: [
      "1. Use esta API para simular notificações de pagamento",
      "2. O frontend deve escutar eventos 'pixPaymentConfirmed'",
      "3. Quando receber o evento, deve mostrar modal de sucesso",
      "4. Útil para testar a interface sem fazer pagamentos reais"
    ],
    example_javascript: `
// No frontend, escutar o evento:
window.addEventListener('pixPaymentConfirmed', (event) => {
  console.log('Pagamento confirmado!', event.detail);
  // Mostrar modal de sucesso
  showPaymentSuccessModal(event.detail);
});

// Para disparar manualmente:
window.dispatchEvent(new CustomEvent('pixPaymentConfirmed', {
  detail: {
    codigo: 'BLT175312844531027L5H1MB',
    valor: 0.13,
    data: new Date().toLocaleString('pt-BR'),
    transactionId: 'pixi_01k0q8yvfqe3za28fz3fvapa9y'
  }
}));
    `
  })
}
