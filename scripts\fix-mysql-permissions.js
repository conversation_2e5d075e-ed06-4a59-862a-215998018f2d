#!/usr/bin/env node

/**
 * Script para corrigir permissões do MySQL
 * Execute: node scripts/fix-mysql-permissions.js
 */

import mysql from 'mysql2/promise'
import { config } from 'dotenv'

// Load environment variables
config({ path: '.env.local' })

async function fixPermissions() {
  let connection = null
  
  try {
    console.log('🔧 Corrigindo permissões do MySQL...')
    
    // Conectar como root
    const rootConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '3306'),
      user: 'root',
      password: process.env.DB_PASSWORD || '',
      charset: 'utf8mb4',
      multipleStatements: true
    }
    
    connection = await mysql.createConnection(rootConfig)
    console.log('✅ Conectado como root')
    
    const dbName = process.env.DB_NAME || 'sistema-bolao-top'
    const dbUser = process.env.DB_USER || 'root'
    
    console.log(`🔍 Verificando usuário: ${dbUser}`)
    
    // Se não for root, criar/corrigir usuário
    if (dbUser !== 'root') {
      console.log(`🔧 Criando/corrigindo usuário: ${dbUser}`)
      
      // Criar usuário se não existir
      await connection.execute(`
        CREATE USER IF NOT EXISTS '${dbUser}'@'localhost' IDENTIFIED BY '${process.env.DB_PASSWORD || ''}'
      `)
      
      await connection.execute(`
        CREATE USER IF NOT EXISTS '${dbUser}'@'%' IDENTIFIED BY '${process.env.DB_PASSWORD || ''}'
      `)
      
      // Dar todas as permissões
      await connection.execute(`
        GRANT ALL PRIVILEGES ON \`${dbName}\`.* TO '${dbUser}'@'localhost'
      `)
      
      await connection.execute(`
        GRANT ALL PRIVILEGES ON \`${dbName}\`.* TO '${dbUser}'@'%'
      `)
      
      // Aplicar mudanças
      await connection.execute('FLUSH PRIVILEGES')
      
      console.log(`✅ Permissões concedidas para ${dbUser}`)
      
      // Verificar permissões
      const [grants] = await connection.execute(`SHOW GRANTS FOR '${dbUser}'@'localhost'`)
      console.log('📋 Permissões atuais:')
      grants.forEach(grant => {
        console.log(`  - ${Object.values(grant)[0]}`)
      })
    } else {
      console.log('✅ Usando usuário root - permissões já são completas')
    }
    
    // Testar conexão com o usuário configurado
    console.log('🧪 Testando conexão com usuário configurado...')
    
    const testConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '3306'),
      user: dbUser,
      password: process.env.DB_PASSWORD || '',
      database: dbName,
      charset: 'utf8mb4'
    }
    
    const testConnection = await mysql.createConnection(testConfig)
    
    // Testar operações
    await testConnection.execute('SELECT 1')
    console.log('✅ SELECT: OK')
    
    // Testar se pode fazer DELETE (sem executar realmente)
    await testConnection.execute(`SELECT COUNT(*) FROM boloes WHERE id = 999999`)
    console.log('✅ Acesso à tabela boloes: OK')
    
    await testConnection.end()
    
    console.log('🎉 Todas as permissões estão corretas!')
    
  } catch (error) {
    console.error('❌ Erro ao corrigir permissões:', error.message)
    
    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.error('\n💡 Erro de acesso:')
      console.error('   - Verifique se você tem acesso root ao MySQL')
      console.error('   - Verifique a senha do root no .env.local')
    }
    
    process.exit(1)
  } finally {
    if (connection) {
      await connection.end()
    }
  }
}

// Executar
fixPermissions().catch(console.error)
