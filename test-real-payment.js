#!/usr/bin/env node

// Teste de pagamento real via webhook
import fetch from 'node-fetch';

async function testRealPayment() {
  console.log('💳 TESTE DE PAGAMENTO REAL VIA WEBHOOK');
  console.log('=' .repeat(50));

  try {
    // 1. Buscar bilhetes pendentes
    console.log('\n📋 1. Buscando bilhetes pendentes...');
    const pendentesResponse = await fetch('http://localhost:3000/api/simulate-pix-payment');
    const pendentesData = await pendentesResponse.json();
    
    console.log(`📊 Bilhetes pendentes: ${pendentesData.bilhetes_pendentes}`);
    
    if (pendentesData.bilhetes_pendentes === 0) {
      console.log('\n⚠️ Nenhum bilhete pendente encontrado');
      console.log('💡 Crie um bilhete primeiro na interface');
      return;
    }

    const bilhete = pendentesData.bilhetes[0];
    console.log(`\n🎫 Testando com bilhete: ${bilhete.codigo}`);
    console.log(`💰 Valor: R$ ${bilhete.valor}`);
    console.log(`🔗 Transaction ID: ${bilhete.transaction_id}`);

    // 2. Simular webhook de pagamento APROVADO
    console.log('\n✅ 2. Simulando webhook de PAGAMENTO APROVADO...');
    const webhookAprovado = await fetch('http://localhost:3000/api/v1/MP/webhookruntransation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        order_id: bilhete.transaction_id,
        status: 'PAID',
        type: 'PIXOUT',
        message: 'Pagamento aprovado via PIX',
        amount: parseFloat(bilhete.valor),
        payment_method: 'PIX',
        timestamp: new Date().toISOString()
      })
    });
    
    const resultAprovado = await webhookAprovado.json();
    
    if (resultAprovado.success) {
      console.log('🎉 PAGAMENTO APROVADO COM SUCESSO!');
      console.log(`📊 Status anterior: ${resultAprovado.bilhete_original?.status || 'N/A'}`);
      console.log(`📊 Status atual: ${resultAprovado.bilhete_atualizado?.status || 'N/A'}`);
      console.log(`🔄 Linhas afetadas: ${resultAprovado.linhas_afetadas || 0}`);
    } else {
      console.log('❌ Erro no webhook de aprovação:', resultAprovado.message);
    }

    // 3. Aguardar um pouco e verificar status
    console.log('\n⏳ 3. Aguardando 2 segundos...');
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 4. Verificar status atualizado
    console.log('\n🔍 4. Verificando status atualizado...');
    const statusResponse = await fetch(`http://localhost:3000/api/pix/check-status?transaction_id=${bilhete.transaction_id}`);
    const statusData = await statusResponse.json();
    
    console.log(`📊 Status final: ${statusData.status}`);
    console.log(`🔄 Foi atualizado: ${statusData.updated ? 'SIM' : 'NÃO'}`);

    // 5. Testar webhook de CANCELAMENTO
    console.log('\n❌ 5. Testando webhook de CANCELAMENTO...');
    const webhookCancelado = await fetch('http://localhost:3000/api/v1/MP/webhookruntransation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        order_id: bilhete.transaction_id,
        status: 'CANCELLED',
        type: 'PIXOUT',
        message: 'Pagamento cancelado pelo usuário',
        timestamp: new Date().toISOString()
      })
    });
    
    const resultCancelado = await webhookCancelado.json();
    
    if (resultCancelado.success) {
      console.log('🚫 PAGAMENTO CANCELADO!');
      console.log(`📊 Status atual: ${resultCancelado.bilhete_atualizado?.status || 'N/A'}`);
    }

    // 6. Testar webhook de PENDENTE
    console.log('\n⏳ 6. Testando webhook de PENDENTE...');
    const webhookPendente = await fetch('http://localhost:3000/api/v1/MP/webhookruntransation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        order_id: bilhete.transaction_id,
        status: 'PENDING',
        type: 'PIXOUT',
        message: 'Pagamento ainda pendente',
        timestamp: new Date().toISOString()
      })
    });
    
    const resultPendente = await webhookPendente.json();
    
    if (resultPendente.success) {
      console.log('⏳ PAGAMENTO PENDENTE!');
      console.log(`📊 Status atual: ${resultPendente.bilhete_atualizado?.status || 'N/A'}`);
    }

    // 7. Resultado final
    console.log('\n🎯 RESULTADO FINAL:');
    console.log('=' .repeat(30));
    
    if (resultAprovado.success && resultCancelado.success && resultPendente.success) {
      console.log('🎉 ✅ WEBHOOK FUNCIONANDO 100%!');
      console.log('');
      console.log('✅ Webhook de APROVAÇÃO: OK');
      console.log('✅ Webhook de CANCELAMENTO: OK');
      console.log('✅ Webhook de PENDENTE: OK');
      console.log('✅ Atualização no banco: OK');
      
      console.log('\n💡 COMO USAR:');
      console.log('');
      console.log('1. 🎫 Usuário cria bilhete na interface');
      console.log('2. 📱 Usuário escaneia QR Code PIX');
      console.log('3. 💳 Usuário faz pagamento no app do banco');
      console.log('4. 🔔 Servidor PIX chama webhook automaticamente');
      console.log('5. 🎉 Sistema atualiza status e mostra modal');
      
      console.log('\n🔗 WEBHOOK URL:');
      console.log('https://ouroemu.site/api/v1/MP/webhookruntransation');
      console.log('http://localhost:3000/api/v1/MP/webhookruntransation');
      
    } else {
      console.log('❌ ⚠️ WEBHOOK COM PROBLEMAS');
      console.log(`📊 Aprovação: ${resultAprovado.success ? 'OK' : 'ERRO'}`);
      console.log(`📊 Cancelamento: ${resultCancelado.success ? 'OK' : 'ERRO'}`);
      console.log(`📊 Pendente: ${resultPendente.success ? 'OK' : 'ERRO'}`);
    }

  } catch (error) {
    console.error('❌ Erro no teste:', error.message);
  }

  console.log('\n' + '=' .repeat(50));
  console.log('🏁 Teste finalizado');
}

// Executar teste
testRealPayment().catch(console.error);
