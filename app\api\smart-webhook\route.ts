import { NextRequest, NextResponse } from "next/server"

// Smart Webhook API - Tenta remoto primeiro, depois local
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { order_id, status, type, message } = body

    console.log("🚀 Smart Webhook iniciado para:", { order_id, status, type })

    const webhookData = {
      order_id,
      status: status || "PAID",
      type: type || "PIXOUT", 
      message: message || "Payment approved",
      timestamp: new Date().toISOString()
    }

    const WEBHOOK_URLS = {
      remote: 'https://ouroemu.site/api/v1/MP/webhookruntransation',
      local: `${request.nextUrl.origin}/api/v1/MP/webhookruntransation`
    }

    // Função para chamar webhook com timeout
    async function callWebhook(url: string, timeout = 8000) {
      try {
        console.log(`🔗 Tentando webhook: ${url}`)
        
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), timeout)
        
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'Smart-Webhook-API/1.0'
          },
          body: JSON.stringify(webhookData),
          signal: controller.signal
        })
        
        clearTimeout(timeoutId)
        
        if (response.ok) {
          const result = await response.json()
          return { success: true, data: result, url }
        } else {
          return { 
            success: false, 
            error: `HTTP ${response.status}: ${response.statusText}`, 
            url 
          }
        }
        
      } catch (error: any) {
        return { 
          success: false, 
          error: error.message, 
          url 
        }
      }
    }

    // Tentar webhook remoto primeiro
    console.log('🌐 Tentativa 1: Webhook Remoto')
    const remoteResult = await callWebhook(WEBHOOK_URLS.remote, 6000)
    
    if (remoteResult.success) {
      console.log('✅ Webhook remoto funcionou!')
      return NextResponse.json({
        success: true,
        method: 'remote',
        message: 'Pagamento processado via webhook remoto',
        webhook_response: remoteResult.data,
        order_id,
        processed_at: new Date().toISOString()
      })
    } else {
      console.log(`❌ Webhook remoto falhou: ${remoteResult.error}`)
    }
    
    // Tentar webhook local como fallback
    console.log('🏠 Tentativa 2: Webhook Local (Fallback)')
    const localResult = await callWebhook(WEBHOOK_URLS.local, 5000)
    
    if (localResult.success) {
      console.log('✅ Webhook local funcionou!')
      return NextResponse.json({
        success: true,
        method: 'local',
        message: 'Pagamento processado via webhook local (fallback)',
        webhook_response: localResult.data,
        order_id,
        processed_at: new Date().toISOString(),
        fallback_reason: remoteResult.error
      })
    } else {
      console.log(`❌ Webhook local falhou: ${localResult.error}`)
    }
    
    // Ambos falharam
    console.log('❌ ERRO: Ambos os webhooks falharam!')
    return NextResponse.json({
      success: false,
      message: 'Falha em todos os webhooks',
      errors: {
        remote: remoteResult.error,
        local: localResult.error
      },
      order_id,
      processed_at: new Date().toISOString()
    }, { status: 500 })

  } catch (error: any) {
    console.error('❌ Erro no Smart Webhook:', error)
    return NextResponse.json({
      success: false,
      message: 'Erro interno no smart webhook',
      error: error.message
    }, { status: 500 })
  }
}

// Método GET para testar
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const order_id = searchParams.get('order_id')
  
  if (!order_id) {
    return NextResponse.json({
      message: 'Smart Webhook API',
      usage: {
        post: 'POST /api/smart-webhook - Processa pagamento com fallback automático',
        get: 'GET /api/smart-webhook?order_id=CODIGO - Testa webhook para bilhete específico'
      },
      endpoints: {
        remote: 'https://ouroemu.site/api/v1/MP/webhookruntransation',
        local: '/api/v1/MP/webhookruntransation'
      }
    })
  }
  
  // Testar webhook para bilhete específico
  return POST(new NextRequest(request.url, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      order_id,
      status: 'PAID',
      type: 'PIXOUT',
      message: 'Test payment via GET'
    })
  }))
}
