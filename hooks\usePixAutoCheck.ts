'use client'

import { useEffect, useRef } from 'react'

export function usePixAutoCheck() {
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const lastBilhetesRef = useRef<string>('')

  useEffect(() => {
    // Função para executar a verificação automática
    const executarVerificacaoPixAutomatica = async () => {
      try {
        console.log('🔄 Executando verificação automática de PIX...')

        // Verificar bilhetes do usuário atual
        let userId = localStorage.getItem('user_id')

        // Se não encontrar user_id, tentar pegar do objeto user
        if (!userId) {
          const userStr = localStorage.getItem('user')
          if (userStr) {
            try {
              const user = JSON.parse(userStr)
              userId = user.id?.toString()
              console.log('🔍 User ID encontrado no objeto user:', userId)
            } catch (e) {
              console.log('❌ Erro ao parsear user do localStorage')
            }
          }
        }

        // Se ainda não encontrar, tentar sessionStorage
        if (!userId) {
          userId = sessionStorage.getItem('user_id')
        }

        // Tentar pegar de um cookie
        if (!userId) {
          const cookies = document.cookie.split(';')
          const userCookie = cookies.find(cookie => cookie.trim().startsWith('user_id='))
          if (userCookie) {
            userId = userCookie.split('=')[1]
          }
        }

        if (!userId) {
          console.log('⚠️ Usuário não logado, pulando verificação PIX')
          console.log('🔍 localStorage.user:', localStorage.getItem('user'))
          console.log('🔍 localStorage.user_id:', localStorage.getItem('user_id'))
          return
        }

        console.log(`🔍 Verificando bilhetes para usuário: ${userId}`)

        // Primeiro, tentar verificar via webhook automático
        try {
          console.log('🔔 Tentando webhook automático...')
          const webhookResponse = await fetch('/api/auto-webhook', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              user_id: userId,
              check_recent: true
            })
          })

          if (webhookResponse.ok) {
            const webhookData = await webhookResponse.json()
            console.log('✅ Webhook automático executado:', webhookData.message)
            if (webhookData.bilhetes_atualizados > 0) {
              console.log(`🎉 ${webhookData.bilhetes_atualizados} bilhetes atualizados via webhook!`)
            }
          }
        } catch (webhookError) {
          console.log('⚠️ Webhook automático falhou:', webhookError.message)
        }

        // Buscar todos os bilhetes do usuário
        const bilhetesResponse = await fetch(`/api/user/bilhetes?user_id=${userId}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          }
        })

        if (!bilhetesResponse.ok) {
          console.error('❌ Erro ao buscar bilhetes:', bilhetesResponse.status)
          return
        }

        const bilhetesData = await bilhetesResponse.json()
        const bilhetes = bilhetesData.bilhetes || []

        // Criar hash dos bilhetes para detectar mudanças
        const bilhetesHash = JSON.stringify(bilhetes.map((b: any) => ({
          codigo: b.codigo,
          status: b.status,
          updated_at: b.updated_at
        })))

        console.log(`📊 Hash atual: ${bilhetesHash.substring(0, 100)}...`)
        console.log(`📊 Hash anterior: ${lastBilhetesRef.current?.substring(0, 100) || 'nenhum'}...`)

        // Se houve mudança nos bilhetes
        if (lastBilhetesRef.current && lastBilhetesRef.current !== bilhetesHash) {
          console.log('🔄 Detectada mudança nos bilhetes!')

          // Verificar se algum bilhete mudou para "pago"
          const bilhetesPagos = bilhetes.filter((b: any) => b.status === 'pago')
          console.log(`💳 Bilhetes pagos encontrados: ${bilhetesPagos.length}`)

          for (const bilhete of bilhetesPagos) {
            try {
              // Verificar se este bilhete não estava pago antes
              const lastBilhetesData = lastBilhetesRef.current ? JSON.parse(lastBilhetesRef.current) : []
              const bilheteAnterior = lastBilhetesData.find((b: any) => b.codigo === bilhete.codigo)

              console.log(`🔍 Verificando bilhete ${bilhete.codigo}:`)
              console.log(`   Status atual: ${bilhete.status}`)
              console.log(`   Status anterior: ${bilheteAnterior?.status || 'não encontrado'}`)

              if (!bilheteAnterior || bilheteAnterior.status !== 'pago') {
                console.log(`🎉 BILHETE ${bilhete.codigo} FOI PAGO! Disparando evento...`)

                // Verificar se o pagamento foi confirmado via webhook real
                const agora = new Date()
                let atualizadoEm = new Date()

                // Tentar diferentes formatos de data
                if (bilhete.updated_at) {
                  try {
                    atualizadoEm = new Date(bilhete.updated_at)
                    if (isNaN(atualizadoEm.getTime())) {
                      throw new Error('Data inválida')
                    }
                  } catch (error) {
                    console.log('⚠️ Erro ao parsear updated_at, usando data atual')
                    atualizadoEm = new Date()
                  }
                } else if (bilhete.created_at) {
                  try {
                    atualizadoEm = new Date(bilhete.created_at)
                    if (isNaN(atualizadoEm.getTime())) {
                      throw new Error('Data inválida')
                    }
                  } catch (error) {
                    console.log('⚠️ Erro ao parsear created_at, usando data atual')
                    atualizadoEm = new Date()
                  }
                }

                const diferencaMinutos = (agora.getTime() - atualizadoEm.getTime()) / (1000 * 60)

                console.log(`📅 Verificação de tempo:`)
                console.log(`   Agora: ${agora.toISOString()}`)
                console.log(`   Atualizado em: ${atualizadoEm.toISOString()}`)
                console.log(`   Diferença: ${diferencaMinutos.toFixed(1)} minutos`)

                // Sempre disparar evento para pagamentos recém confirmados
                // Aumentar limite para 10 minutos para garantir que funcione
                if (diferencaMinutos <= 10 || isNaN(diferencaMinutos)) {
                  console.log(`✅ Disparando modal de sucesso para ${bilhete.codigo}`)

                  // Disparar evento customizado para mostrar modal de sucesso
                  const event = new CustomEvent('pixPaymentConfirmed', {
                    detail: {
                      codigo: bilhete.codigo,
                      valor: bilhete.valor_total || bilhete.valor,
                      data: new Date().toLocaleString('pt-BR'),
                      transactionId: bilhete.transaction_id || bilhete.codigo,
                      timestamp: Date.now(),
                      paymentConfirmed: true
                    }
                  })

                  window.dispatchEvent(event)
                  console.log(`✅ Evento pixPaymentConfirmed disparado para ${bilhete.codigo}`)
                } else {
                  console.log(`⏳ Bilhete pago há ${diferencaMinutos.toFixed(1)} min - não disparar modal`)
                }
              }
            } catch (error) {
              console.error(`❌ Erro ao processar bilhete ${bilhete.codigo}:`, error)
            }
          }
        } else if (!lastBilhetesRef.current) {
          console.log('🆕 Primeira verificação - salvando estado inicial')
        } else {
          console.log('✅ Nenhuma mudança detectada nos bilhetes')
        }

        // Atualizar referência
        lastBilhetesRef.current = bilhetesHash

      } catch (error) {
        console.error('❌ Erro na verificação automática PIX:', error)
      }
    }

    // Executar verificação imediatamente ao carregar
    executarVerificacaoPixAutomatica()

    // Configurar intervalo de 5 segundos para verificação rápida
    // Isso permite detecção quase instantânea de pagamentos
    intervalRef.current = setInterval(executarVerificacaoPixAutomatica, 5 * 1000)

    // Cleanup ao desmontar
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
      }
    }
  }, [])

  return null
}
