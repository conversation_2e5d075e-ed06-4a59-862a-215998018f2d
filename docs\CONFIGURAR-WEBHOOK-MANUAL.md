# 🔧 Como Configurar Webhook na API PIX (meiodepagamento.com)

## 🎯 Objetivo
Configurar o webhook para que a API PIX envie notificações automáticas quando um pagamento for confirmado.

---

## 📋 Dados para Configuração

### 🔗 URL do Webhook:
```
https://ouroemu.site/api/v1/MP/webhookruntransation
```

### 🎯 Eventos para Ativar:
- ✅ `payment.paid` (Pagamento confirmado)
- ✅ `payment.pending` (Pagamento pendente)  
- ✅ `payment.cancelled` (Pagamento cancelado)

### 🔧 Configurações Técnicas:
- **Método**: POST
- **Content-Type**: application/json
- **Status**: Ativo
- **Timeout**: 30 segundos

---

## 🚀 Passo a Passo

### 1. **Acesse o Painel da API**
- Vá para: https://api.meiodepagamento.com
- Faça login com suas credenciais

### 2. **Encontre a Seção de Webhooks**
Procure por uma das seguintes seções:
- 🔔 "Webhooks"
- ⚙️ "Configurações"
- 📡 "Notificações"
- 🔧 "Integrações"
- ⚡ "Callbacks"

### 3. **Configure o Webhook**
Adicione um novo webhook com os dados:

```json
{
  "url": "https://ouroemu.site/api/v1/MP/webhookruntransation",
  "events": [
    "payment.paid",
    "payment.pending", 
    "payment.cancelled"
  ],
  "method": "POST",
  "active": true
}
```

### 4. **Teste a Configuração**
- Salve as configurações
- Faça um pagamento PIX de teste
- Verifique se o webhook foi chamado

---

## 🧪 Como Testar se Funcionou

### Teste 1: Verificar Logs
```bash
# Verificar se webhook está recebendo chamadas
curl -X GET "https://ouroemu.site/api/webhook-status"
```

### Teste 2: Simular Pagamento
```bash
# Simular um pagamento para testar
curl -X POST https://ouroemu.site/api/simulate-pix-payment \
  -H "Content-Type: application/json" \
  -d '{"transaction_id":"SEU_TRANSACTION_ID","simulate_payment":true}'
```

### Teste 3: Verificar Modal
- Faça apostas na aplicação
- Gere QR Code PIX
- Pague o PIX
- **🎉 Modal deve aparecer automaticamente!**

---

## 📱 Formato do Payload Esperado

Quando configurado corretamente, a API PIX enviará:

```json
{
  "qr_code_payment_id": "pixi_01k0qf8pvjf0hv3qk7phzk97qj",
  "transaction_id": "pixi_01k0qf8pvjf0hv3qk7phzk97qj",
  "order_id": "pixi_01k0qf8pvjf0hv3qk7phzk97qj",
  "amount": "0.13",
  "description": "Bolão - BLT123...",
  "status": "PAID",
  "end_to_end_id": "E1753134730845",
  "last_updated_at": "2025-07-21T21:52:10.845Z",
  "error": null
}
```

---

## 🔍 Troubleshooting

### ❌ Webhook não está sendo chamado:
1. Verifique se a URL está correta
2. Confirme que os eventos estão ativos
3. Teste a conectividade da URL
4. Verifique logs da API PIX

### ❌ Modal não aparece:
1. Verifique se o webhook está sendo recebido
2. Confirme se o status está sendo atualizado
3. Teste com simulação manual
4. Verifique console do navegador

### ❌ Erro de conectividade:
1. Confirme que https://ouroemu.site está acessível
2. Teste o endpoint manualmente
3. Verifique firewall/proxy

---

## 🎉 Resultado Esperado

Quando tudo estiver configurado:

```mermaid
sequenceDiagram
    participant Cliente
    participant App
    participant PIX_API as API PIX
    participant Webhook as Seu Sistema
    participant Frontend

    Cliente->>App: Faz apostas
    App->>PIX_API: Gera QR Code
    Cliente->>PIX_API: Paga PIX
    PIX_API->>Webhook: 🔔 Chama webhook automaticamente
    Webhook->>Webhook: Atualiza status para "pago"
    Frontend->>Frontend: 🎉 Detecta mudança e mostra modal
```

**🎯 Fluxo Final:**
1. Cliente paga PIX
2. API detecta pagamento
3. Webhook é chamado automaticamente
4. Status é atualizado
5. **🎉 Modal aparece automaticamente!**

---

## 📞 Suporte

Se precisar de ajuda:
- 📧 **Email**: <EMAIL>
- 📱 **WhatsApp**: 43988574313
- 🔧 **Status**: https://ouroemu.site/api/webhook-status

---

**✅ Após configurar, o sistema funcionará 100% automaticamente!**
