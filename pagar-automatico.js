#!/usr/bin/env node

// Comando para pagar bilhetes automaticamente usando o sistema inteligente
import fetch from 'node-fetch';

async function pagarAutomatico(codigo) {
  try {
    console.log(`🚀 Iniciando pagamento automático para: ${codigo}`);
    
    // Usar o simulador de pagamento PIX que chama o smart webhook
    const response = await fetch('http://localhost:3000/api/simulate-pix-payment', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        bilhete_codigo: codigo
      })
    });
    
    const result = await response.json();
    
    if (result.success) {
      console.log('✅ PAGAMENTO PROCESSADO COM SUCESSO!');
      console.log(`🎫 Bilhete: ${result.bilhete_original.codigo}`);
      console.log(`💰 Valor: R$ ${result.bilhete_original.valor}`);
      console.log(`📊 Status anterior: ${result.bilhete_original.status}`);
      console.log(`📊 Status atual: ${result.bilhete_atualizado?.status || 'N/A'}`);
      
      if (result.webhook_result) {
        console.log(`🔗 Método webhook: ${result.webhook_result.method}`);
        if (result.webhook_result.fallback_reason) {
          console.log(`⚠️ Fallback usado: ${result.webhook_result.fallback_reason}`);
        }
      }
      
      if (result.bilhete_atualizado?.status === 'pago') {
        console.log('\n🎉 BILHETE PAGO COM SUCESSO!');
        console.log('✅ O sistema automático funcionou perfeitamente');
      } else {
        console.log('\n⚠️ Webhook executado mas status pode não ter sido atualizado');
      }
      
      return true;
    } else {
      console.log(`❌ ERRO: ${result.message}`);
      if (result.error) {
        console.log(`🔍 Detalhes: ${result.error}`);
      }
      return false;
    }
    
  } catch (error) {
    console.error(`❌ Erro: ${error.message}`);
    return false;
  }
}

async function listarPendentes() {
  try {
    console.log('📋 Listando bilhetes pendentes...');
    
    const response = await fetch('http://localhost:3000/api/simulate-pix-payment');
    const data = await response.json();
    
    if (data.bilhetes_pendentes > 0) {
      console.log(`\n📊 Encontrados ${data.bilhetes_pendentes} bilhetes pendentes:`);
      
      data.bilhetes.forEach((bilhete, index) => {
        console.log(`\n${index + 1}. ${bilhete.codigo}`);
        console.log(`   💰 Valor: R$ ${bilhete.valor}`);
        console.log(`   🕐 Criado: ${new Date(bilhete.created_at).toLocaleString('pt-BR')}`);
        console.log(`   📝 Comando: node pagar-automatico.js ${bilhete.codigo}`);
      });
    } else {
      console.log('\n✅ Nenhum bilhete pendente encontrado');
      console.log('💡 Todos os bilhetes estão pagos ou não há bilhetes recentes');
    }
    
  } catch (error) {
    console.error(`❌ Erro ao listar: ${error.message}`);
  }
}

async function main() {
  const args = process.argv.slice(2);
  
  console.log('💳 Sistema de Pagamento Automático');
  console.log('=' .repeat(40));
  
  if (args.length === 0) {
    console.log('\n📝 Uso:');
    console.log('  node pagar-automatico.js <CODIGO_BILHETE>  - Pagar bilhete específico');
    console.log('  node pagar-automatico.js --list           - Listar bilhetes pendentes');
    console.log('\n📝 Exemplos:');
    console.log('  node pagar-automatico.js BLT175312781400027NURFYD');
    console.log('  node pagar-automatico.js --list');
    
    // Mostrar bilhetes pendentes por padrão
    await listarPendentes();
    return;
  }
  
  if (args[0] === '--list' || args[0] === '-l') {
    await listarPendentes();
    return;
  }
  
  const codigo = args[0];
  console.log(`\n🎯 Processando bilhete: ${codigo}`);
  
  const success = await pagarAutomatico(codigo);
  
  if (success) {
    console.log('\n🎉 PROCESSO CONCLUÍDO COM SUCESSO!');
  } else {
    console.log('\n💔 PROCESSO FALHOU');
    process.exit(1);
  }
}

main().catch(console.error);
