<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Método não permitido']);
    exit;
}

// Configuração do banco de dados
$host = 'localhost';
$dbname = 'loteria_db';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erro de conexão com banco de dados']);
    exit;
}

// Ler dados da requisição
$input = json_decode(file_get_contents('php://input'), true);
$user_id = $input['user_id'] ?? null;
$check_recent = $input['check_recent'] ?? false;

if (!$user_id) {
    http_response_code(400);
    echo json_encode(['error' => 'user_id é obrigatório']);
    exit;
}

$bilhetes_atualizados = 0;
$mensagens = [];

try {
    // Buscar bilhetes pendentes do usuário
    $where_clause = "WHERE user_id = :user_id AND status = 'pendente'";
    $params = [':user_id' => $user_id];
    
    // Se check_recent for true, verificar apenas bilhetes criados nas últimas 2 horas
    if ($check_recent) {
        $where_clause .= " AND created_at >= DATE_SUB(NOW(), INTERVAL 2 HOUR)";
    }
    
    $stmt = $pdo->prepare("
        SELECT id, codigo, transaction_id, valor_total, created_at 
        FROM bilhetes 
        $where_clause
        ORDER BY created_at DESC
    ");
    
    $stmt->execute($params);
    $bilhetes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $mensagens[] = "Encontrados " . count($bilhetes) . " bilhetes pendentes";
    
    foreach ($bilhetes as $bilhete) {
        if (!$bilhete['transaction_id']) {
            continue;
        }
        
        $mensagens[] = "Verificando bilhete {$bilhete['codigo']} (transaction: {$bilhete['transaction_id']})";
        
        // Verificar status na API do PIX
        $api_url = "https://api.openpix.com.br/api/v1/charge/{$bilhete['transaction_id']}";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $api_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Q2xpZW50X0lkXzJlNzJkNzJlLTNkNzMtNGJhNy1hNzE5LTNkNzNkNzJlM2Q3Mw==',
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($http_code === 200 && $response) {
            $data = json_decode($response, true);
            
            if ($data && isset($data['charge']) && $data['charge']['status'] === 'COMPLETED') {
                $mensagens[] = "✅ Pagamento confirmado para bilhete {$bilhete['codigo']}";
                
                // Atualizar status no banco
                $update_stmt = $pdo->prepare("
                    UPDATE bilhetes 
                    SET status = 'pago', updated_at = NOW() 
                    WHERE id = :id
                ");
                
                if ($update_stmt->execute([':id' => $bilhete['id']])) {
                    $bilhetes_atualizados++;
                    $mensagens[] = "✅ Status atualizado no banco para bilhete {$bilhete['codigo']}";
                } else {
                    $mensagens[] = "❌ Erro ao atualizar status no banco para bilhete {$bilhete['codigo']}";
                }
            } else {
                $status = $data['charge']['status'] ?? 'unknown';
                $mensagens[] = "⏳ Bilhete {$bilhete['codigo']} ainda pendente (status: $status)";
            }
        } else {
            $mensagens[] = "❌ Erro ao verificar API para bilhete {$bilhete['codigo']} (HTTP: $http_code)";
        }
        
        // Pequena pausa entre verificações
        usleep(100000); // 0.1 segundo
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Verificação automática concluída',
        'bilhetes_verificados' => count($bilhetes),
        'bilhetes_atualizados' => $bilhetes_atualizados,
        'detalhes' => $mensagens
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Erro interno do servidor',
        'message' => $e->getMessage()
    ]);
}
?>
