#!/bin/bash

# Script de Deploy para Produção - Sistema <PERSON>
# Execute: bash scripts/deploy.sh

echo "🚀 Iniciando deploy para produção..."

# 1. Verificar se o banco está funcionando
echo "🔍 Verificando banco de dados..."
npm run db:test
if [ $? -ne 0 ]; then
    echo "❌ Erro: Banco de dados não está funcionando!"
    echo "💡 Execute: npm run db:setup"
    exit 1
fi

# 2. Fazer build de produção
echo "🔨 Fazendo build de produção..."
npm run build
if [ $? -ne 0 ]; then
    echo "❌ Erro no build!"
    exit 1
fi

# 3. Parar servidor anterior (se estiver rodando)
echo "🛑 Parando servidor anterior..."
pkill -f "next start" || true

# 4. Iniciar servidor de produção
echo "🚀 Iniciando servidor de produção..."
NODE_ENV=production npm start &

# 5. Aguardar servidor inicializar
echo "⏳ Aguardando servidor inicializar..."
sleep 5

# 6. Testar se está funcionando
echo "🧪 Testando servidor..."
curl -f http://localhost:3000/api/boloes > /dev/null
if [ $? -eq 0 ]; then
    echo "✅ Deploy concluído com sucesso!"
    echo "🌐 Servidor rodando em: http://localhost:3000"
else
    echo "❌ Erro: Servidor não está respondendo!"
    exit 1
fi

echo "📋 Logs do servidor:"
echo "   - Para ver logs: tail -f logs/production.log"
echo "   - Para parar: pkill -f 'next start'"
echo "   - Para reiniciar: bash scripts/deploy.sh"
