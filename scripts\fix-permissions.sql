-- Script para corrigir permissões do usuário MySQL
-- Execute: mysql -u root -p < scripts/fix-permissions.sql

-- Verificar usuários existentes
SELECT User, Host FROM mysql.user WHERE User LIKE '%sistema-bolao%';

-- <PERSON> todas as permissões para o usuário sistema-bolao-top
GRANT ALL PRIVILEGES ON `sistema-bolao-top`.* TO 'sistema-bolao-top'@'localhost';
GRANT ALL PRIVILEGES ON `sistema-bolao-top`.* TO 'sistema-bolao-top'@'%';

-- Dar permissões específicas se o usuário não existir
CREATE USER IF NOT EXISTS 'sistema-bolao-top'@'localhost' IDENTIFIED BY '';
CREATE USER IF NOT EXISTS 'sistema-bolao-top'@'%' IDENTIFIED BY '';

-- <PERSON> to<PERSON> as permissões necessárias
GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, DROP, ALTER, INDEX ON `sistema-bolao-top`.* TO 'sistema-bolao-top'@'localhost';
GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, DROP, ALTER, INDEX ON `sistema-bolao-top`.* TO 'sistema-bolao-top'@'%';

-- Aplicar as mudanças
FLUSH PRIVILEGES;

-- Verificar permissões
SHOW GRANTS FOR 'sistema-bolao-top'@'localhost';

-- Testar conexão
SELECT 'Permissões aplicadas com sucesso!' as status;
