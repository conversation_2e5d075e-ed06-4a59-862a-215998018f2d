#!/bin/bash

# Script para configurar webhook via API meiodepagamento.com

echo "🔧 Configurando webhook via API..."

# Suas credenciais
PIX_API_URL="https://api.meiodepagamento.com/api/V1"
PIX_TOKEN="Gk0e0W5bc2Guozw97NXv8duZbZCDbsedwMuTQNqqn5ZMT549Z0qDDZBF5ZbG9sHhXw9egbly3eiFJ4PKfs6ur+zzXs30gSDV6CYl8sQmSPJaISqpqf/FtJ2k30O3kO9ZsNwasalD0PGpD2wlAbb4ynO5S07P1kgWZRtw4w=="
WEBHOOK_URL="https://ouroemu.site/api/v1/MP/webhookruntransation"

echo "📍 API: $PIX_API_URL"
echo "🔗 Webhook: $WEBHOOK_URL"

# Payload para configurar webhook
PAYLOAD='{
  "token": "'$PIX_TOKEN'",
  "webhook_url": "'$WEBHOOK_URL'",
  "events": ["payment.paid", "payment.pending", "payment.cancelled"],
  "active": true,
  "method": "POST",
  "content_type": "application/json"
}'

echo "📤 Enviando configuração..."

# Tentar diferentes endpoints
ENDPOINTS=(
  "/webhook/configure"
  "/webhook/config"
  "/webhook/setup"
  "/config/webhook"
  "/settings/webhook"
  "/notification/webhook"
  "/api/webhook"
)

for endpoint in "${ENDPOINTS[@]}"; do
  echo "🔄 Tentando: $PIX_API_URL$endpoint"
  
  response=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $PIX_TOKEN" \
    -d "$PAYLOAD" \
    "$PIX_API_URL$endpoint" 2>/dev/null)
  
  if [[ $? -eq 0 ]] && [[ -n "$response" ]]; then
    echo "✅ Resposta recebida de $endpoint:"
    echo "$response" | jq . 2>/dev/null || echo "$response"
    
    # Verificar se foi sucesso
    if echo "$response" | grep -q -i "success\|sucesso\|ok\|configured"; then
      echo "🎉 Webhook configurado com sucesso via $endpoint!"
      exit 0
    fi
  else
    echo "❌ Falhou: $endpoint"
  fi
  
  echo ""
done

echo "⚠️ Configuração automática não funcionou."
echo "📖 Use a configuração manual no painel web."
