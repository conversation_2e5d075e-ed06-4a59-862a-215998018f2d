# 🔔 Configuração de Webhook Automático - Sistema Bolão

## ✅ Status Atual

O webhook está **FUNCIONANDO PERFEITAMENTE** e pronto para receber notificações automáticas!

- **URL do Webhook**: `https://ouroemu.site/api/v1/MP/webhookruntransation`
- **Status**: ✅ Ativo e testado
- **Último teste**: Bilhete `BLT175313245158527AS0R9Z` processado com sucesso

## 🎯 Como Funciona o Fluxo Automático

```mermaid
sequenceDiagram
    participant Cliente
    participant App
    participant PIX_API as API PIX (meiodepagamento.com)
    participant Webhook as Seu Sistema
    participant Frontend

    Cliente->>App: Gera QR Code PIX
    App->>PIX_API: Solicita QR Code
    PIX_API-->>App: Retorna QR Code
    Cliente->>PIX_API: Paga via PIX
    PIX_API->>Webhook: 🔔 Envia notificação automática
    Webhook->>Webhook: Atualiza status do bilhete
    Frontend->>Frontend: 🎉 Detecta mudança e exibe modal
```

## 🔧 Configuração Necessária na API PIX

Para que o evento seja disparado **automaticamente**, você precisa configurar o webhook na API meiodepagamento.com:

### 1. Acesse o Painel da API PIX
- URL: https://api.meiodepagamento.com (ou painel de administração)
- Faça login com suas credenciais

### 2. Configure o Webhook
Procure por uma seção como "Webhooks", "Notificações" ou "Configurações" e adicione:

```json
{
  "webhook_url": "https://ouroemu.site/api/v1/MP/webhookruntransation",
  "events": ["payment.paid", "payment.pending", "payment.cancelled"],
  "active": true,
  "token": "seu_token_aqui"
}
```

### 3. Eventos Suportados
O webhook deve ser configurado para os seguintes eventos:
- ✅ `payment.paid` - Pagamento confirmado
- ⏳ `payment.pending` - Pagamento pendente  
- ❌ `payment.cancelled` - Pagamento cancelado

## 📋 Formato do Payload Esperado

Quando um pagamento for confirmado, a API PIX deve enviar um POST para o webhook com:

```json
{
  "order_id": "pixi_01k0qdcbzmfmrs95pza2mdsrsn",
  "transaction_id": "pixi_01k0qdcbzmfmrs95pza2mdsrsn", 
  "qr_code_payment_id": "pixi_01k0qdcbzmfmrs95pza2mdsrsn",
  "status": "PAID",
  "type": "PIXOUT",
  "amount": 0.13,
  "description": "Bolão - BLT175313245158527AS0R9Z",
  "end_to_end_id": "E1753133089554",
  "last_updated_at": "2025-07-21T21:24:49.554Z"
}
```

## 🧪 Testando o Webhook

### Teste Manual
```bash
# Testar webhook com bilhete específico
curl -X POST https://ouroemu.site/api/test-webhook-auto \
  -H "Content-Type: application/json" \
  -d '{
    "bilhete_codigo": "BLT175313245158527AS0R9Z",
    "transaction_id": "pixi_01k0qdcbzmfmrs95pza2mdsrsn"
  }'
```

### Teste Automático
```bash
# Executar monitoramento automático
curl -X POST https://ouroemu.site/api/auto-payment-monitor
```

## 🔄 Endpoints Disponíveis

| Endpoint | Método | Descrição |
|----------|--------|-----------|
| `/api/v1/MP/webhookruntransation` | POST | **Webhook principal** (recebe da API PIX) |
| `/api/webhook-trigger` | POST | Disparar webhook manualmente |
| `/api/auto-payment-monitor` | POST | Monitoramento automático |
| `/api/test-webhook-auto` | POST | Teste completo do webhook |
| `/api/webhook-config` | GET | Configurações atuais |

## 🚀 Configuração Automática

Execute o script para tentar configurar automaticamente:

```bash
node scripts/configure-pix-webhook.js
```

## 🔍 Monitoramento e Logs

### Verificar Status
```bash
# Status do webhook
curl https://ouroemu.site/api/v1/MP/webhookruntransation

# Configurações atuais  
curl https://ouroemu.site/api/webhook-config
```

### Logs do Sistema
O sistema registra todos os webhooks recebidos na tabela `webhook_logs`:

```sql
SELECT * FROM webhook_logs 
WHERE processed_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
ORDER BY processed_at DESC;
```

## ⚡ Configuração de Emergência

Se o webhook automático não estiver funcionando, você pode usar o monitoramento ativo:

### 1. Cron Job (Recomendado)
Adicione ao crontab para verificar a cada minuto:
```bash
* * * * * curl -X POST https://ouroemu.site/api/auto-payment-monitor
```

### 2. Verificação Manual
Execute periodicamente:
```bash
curl -X POST https://ouroemu.site/api/auto-payment-monitor
```

## 🎉 Resultado Esperado

Quando tudo estiver configurado corretamente:

1. **Cliente paga PIX** → QR Code ou Copia e Cola
2. **API PIX detecta** → Pagamento confirmado
3. **Webhook disparado** → Automaticamente para seu sistema
4. **Status atualizado** → Bilhete marcado como "pago"
5. **Frontend atualiza** → Modal de sucesso aparece
6. **🎉 Evento disparado!** → Automaticamente

## 📞 Suporte

Se precisar de ajuda:
- 📧 Email: <EMAIL>
- 📱 WhatsApp: 43988574313
- 🔧 Logs: `/api/debug-bilhete`

---

**✅ Status**: Webhook configurado e testado com sucesso!  
**🔄 Última atualização**: 21/07/2025 18:24:50  
**🎯 Próximo passo**: Configurar na API meiodepagamento.com
