#!/usr/bin/env node

// Teste completo do sistema de pagamento automático
import fetch from 'node-fetch';

async function testAutoPayment() {
  console.log('🧪 Teste do Sistema de Pagamento Automático');
  console.log('=' .repeat(50));

  try {
    // 1. Verificar bilhetes pendentes
    console.log('\n📊 1. Verificando bilhetes pendentes...');
    const pendentesResponse = await fetch('http://localhost:3000/api/simulate-pix-payment');
    const pendentesData = await pendentesResponse.json();
    
    console.log(`📋 Bilhetes pendentes encontrados: ${pendentesData.bilhetes_pendentes}`);
    
    if (pendentesData.bilhetes_pendentes > 0) {
      const bilhete = pendentesData.bilhetes[0];
      console.log(`🎫 Testando com bilhete: ${bilhete.codigo}`);
      console.log(`💰 Valor: R$ ${bilhete.valor}`);
      
      // 2. Simular pagamento PIX
      console.log('\n💳 2. Simulando pagamento PIX...');
      const paymentResponse = await fetch('http://localhost:3000/api/simulate-pix-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          bilhete_codigo: bilhete.codigo,
          transaction_id: bilhete.transaction_id,
          valor: bilhete.valor
        })
      });
      
      const paymentResult = await paymentResponse.json();
      
      if (paymentResult.success) {
        console.log('✅ Pagamento simulado com sucesso!');
        console.log(`📊 Status anterior: ${paymentResult.bilhete_original.status}`);
        console.log(`📊 Status atual: ${paymentResult.bilhete_atualizado?.status || 'N/A'}`);
        console.log(`🔗 Método webhook: ${paymentResult.webhook_result?.method || 'N/A'}`);
        
        if (paymentResult.bilhete_atualizado?.status === 'pago') {
          console.log('\n🎉 SUCESSO TOTAL! O sistema funcionou perfeitamente:');
          console.log('   ✅ Pagamento PIX detectado');
          console.log('   ✅ Smart webhook executado');
          console.log('   ✅ Bilhete atualizado para PAGO');
          console.log('   ✅ Sistema automático funcionando!');
        } else {
          console.log('\n⚠️ Pagamento processado mas status não foi atualizado');
        }
      } else {
        console.log('❌ Erro na simulação:', paymentResult.message);
      }
      
    } else {
      console.log('\n📝 Nenhum bilhete pendente encontrado.');
      console.log('💡 Para testar:');
      console.log('   1. Acesse http://localhost:3000');
      console.log('   2. Faça login e crie um bilhete');
      console.log('   3. Execute este teste novamente');
    }

    // 3. Verificar estatísticas finais
    console.log('\n📈 3. Verificando estatísticas finais...');
    const statsResponse = await fetch('http://localhost:3000/api/auto-webhook');
    const statsData = await statsResponse.json();
    
    console.log(`📊 Total de bilhetes: ${statsData.estatisticas.total_bilhetes}`);
    console.log(`💳 Bilhetes pagos: ${statsData.estatisticas.pagos}`);
    console.log(`⏳ Bilhetes pendentes: ${statsData.estatisticas.pendentes}`);
    console.log(`🕐 Última hora: ${statsData.estatisticas.ultima_hora}`);

  } catch (error) {
    console.error('❌ Erro no teste:', error.message);
  }

  console.log('\n' + '=' .repeat(50));
  console.log('🏁 Teste finalizado');
}

// Função para testar com um bilhete específico
async function testSpecificBilhete(codigo) {
  console.log(`🎯 Testando bilhete específico: ${codigo}`);
  
  try {
    const response = await fetch('http://localhost:3000/api/simulate-pix-payment', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        bilhete_codigo: codigo
      })
    });
    
    const result = await response.json();
    
    if (result.success) {
      console.log('✅ Teste bem-sucedido!');
      console.log(`📊 Status: ${result.bilhete_atualizado?.status || 'N/A'}`);
      console.log(`🔗 Método: ${result.webhook_result?.method || 'N/A'}`);
    } else {
      console.log('❌ Teste falhou:', result.message);
    }
    
  } catch (error) {
    console.error('❌ Erro:', error.message);
  }
}

// Executar
const args = process.argv.slice(2);

if (args.length > 0) {
  // Testar bilhete específico
  testSpecificBilhete(args[0]);
} else {
  // Teste completo
  testAutoPayment();
}
