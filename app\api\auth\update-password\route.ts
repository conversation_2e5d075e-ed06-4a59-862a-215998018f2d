import { type NextRequest, NextResponse } from "next/server"
import { executeQuery } from "@/lib/database-config"
import bcrypt from 'bcryptjs'

// Força renderização dinâmica para evitar erro de build estático
export const dynamic = 'force-dynamic'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email, nova_senha } = body

    console.log("🔐 Atualizando senha para:", { email })

    // Validações básicas
    if (!email || !nova_senha) {
      return NextResponse.json(
        { error: "Email e nova senha são obrigatórios" },
        { status: 400 }
      )
    }

    if (nova_senha.length < 6) {
      return NextResponse.json(
        { error: "Nova senha deve ter pelo menos 6 caracteres" },
        { status: 400 }
      )
    }

    // Verificar se usuário existe
    const users = await executeQuery(
      "SELECT id, nome FROM usuarios WHERE email = ?",
      [email]
    )

    if (users.length === 0) {
      return NextResponse.json(
        { error: "Usuário não encontrado" },
        { status: 404 }
      )
    }

    const user = users[0]

    // Hash da nova senha
    const novaSenhaHash = await bcrypt.hash(nova_senha, 10)

    // Atualizar senha no banco
    await executeQuery(
      "UPDATE usuarios SET senha = ?, updated_at = NOW() WHERE email = ?",
      [novaSenhaHash, email]
    )

    console.log("✅ Senha atualizada com sucesso para:", user.nome)

    return NextResponse.json({
      success: true,
      message: "Senha atualizada com sucesso",
      user: {
        id: user.id,
        nome: user.nome,
        email: email
      }
    })

  } catch (error: any) {
    console.error("❌ Erro ao atualizar senha:", error)
    
    return NextResponse.json(
      { 
        error: "Erro interno do servidor",
        message: "Não foi possível atualizar a senha"
      },
      { status: 500 }
    )
  }
}
