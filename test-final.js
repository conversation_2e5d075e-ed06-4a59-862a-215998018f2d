#!/usr/bin/env node

// Teste final completo do sistema
import fetch from 'node-fetch';

async function testFinal() {
  console.log('🎯 TESTE FINAL DO SISTEMA DE PAGAMENTO AUTOMÁTICO');
  console.log('=' .repeat(60));

  try {
    // 1. Verificar status do sistema
    console.log('\n🔧 1. Verificando status do sistema...');
    const statusResponse = await fetch('http://localhost:3000/api/auto-webhook');
    const statusData = await statusResponse.json();
    
    console.log(`📊 Sistema: ${statusData.status}`);
    console.log(`📊 Total de bilhetes: ${statusData.estatisticas.total_bilhetes}`);
    console.log(`💳 Bilhetes pagos: ${statusData.estatisticas.pagos}`);
    console.log(`⏳ Bilhetes pendentes: ${statusData.estatisticas.pendentes}`);

    // 2. Listar bilhetes disponíveis
    console.log('\n📋 2. Listando bilhetes disponíveis...');
    const bilhetesResponse = await fetch('http://localhost:3000/api/simulate-pix-payment');
    const bilhetesData = await bilhetesResponse.json();
    
    console.log(`📊 Bilhetes pendentes: ${bilhetesData.bilhetes_pendentes}`);
    
    if (bilhetesData.bilhetes_pendentes > 0) {
      bilhetesData.bilhetes.forEach((bilhete, index) => {
        console.log(`   ${index + 1}. ${bilhete.codigo} - R$ ${bilhete.valor}`);
      });
    }

    // 3. Testar smart webhook
    console.log('\n🚀 3. Testando smart webhook...');
    const webhookResponse = await fetch('http://localhost:3000/api/smart-webhook', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        order_id: 'BLT175312844531027L5H1MB',
        status: 'PAID',
        type: 'PIXOUT',
        message: 'Teste final do sistema'
      })
    });
    
    const webhookResult = await webhookResponse.json();
    
    if (webhookResult.success) {
      console.log(`✅ Smart webhook funcionando!`);
      console.log(`🔗 Método usado: ${webhookResult.method}`);
      if (webhookResult.fallback_reason) {
        console.log(`⚠️ Fallback: ${webhookResult.fallback_reason}`);
      }
    } else {
      console.log(`❌ Smart webhook falhou`);
    }

    // 4. Testar sistema automático
    console.log('\n🤖 4. Testando sistema automático...');
    const autoResponse = await fetch('http://localhost:3000/api/auto-webhook', {
      method: 'POST'
    });
    const autoData = await autoResponse.json();
    
    console.log(`📊 Bilhetes verificados: ${autoData.checked}`);
    console.log(`✅ Bilhetes processados: ${autoData.processed}`);

    // 5. Testar notificação forçada
    console.log('\n🔔 5. Testando notificação forçada...');
    const notificationResponse = await fetch('http://localhost:3000/api/force-payment-notification', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        bilhete_codigo: 'BLT175312844531027L5H1MB',
        user_id: 27
      })
    });
    
    const notificationResult = await notificationResponse.json();
    
    if (notificationResult.success) {
      console.log(`✅ Notificação funcionando!`);
      console.log(`🎫 Bilhete: ${notificationResult.notification.bilhete.codigo}`);
      console.log(`💰 Valor: R$ ${notificationResult.notification.bilhete.valor}`);
    } else {
      console.log(`❌ Notificação falhou: ${notificationResult.message}`);
    }

    // 6. Resultado final
    console.log('\n🎯 RESULTADO FINAL:');
    console.log('=' .repeat(40));
    
    const allWorking = statusData.status === 'ativo' && 
                      webhookResult.success && 
                      notificationResult.success;
    
    if (allWorking) {
      console.log('🎉 ✅ SISTEMA 100% FUNCIONAL!');
      console.log('');
      console.log('✅ Status do sistema: ATIVO');
      console.log('✅ Smart webhook: FUNCIONANDO');
      console.log('✅ Sistema automático: FUNCIONANDO');
      console.log('✅ Notificações: FUNCIONANDO');
      console.log('✅ Fallback automático: CONFIGURADO');
      
      console.log('\n🚀 COMANDOS DISPONÍVEIS:');
      console.log('');
      console.log('📝 Para pagar bilhete específico:');
      console.log('   node pagar-automatico.js BLT175312844531027L5H1MB');
      console.log('');
      console.log('📋 Para listar bilhetes pendentes:');
      console.log('   node pagar-automatico.js --list');
      console.log('');
      console.log('🧪 Para teste completo:');
      console.log('   node test-complete-flow.js');
      console.log('');
      console.log('🔧 Para verificar sistema:');
      console.log('   node test-final.js');
      
      console.log('\n💡 COMO FUNCIONA:');
      console.log('');
      console.log('1. 🌐 Tenta webhook remoto (ouroemu.site) primeiro');
      console.log('2. 🏠 Se falhar, usa webhook local automaticamente');
      console.log('3. 🔄 Sistema monitora pagamentos em tempo real');
      console.log('4. 🎉 Modal de sucesso aparece automaticamente');
      console.log('5. 📱 Interface atualiza em tempo real');
      
      console.log('\n🎯 PRÓXIMOS PASSOS:');
      console.log('');
      console.log('✅ Sistema está pronto para uso!');
      console.log('✅ Crie novos bilhetes na interface');
      console.log('✅ Pagamentos serão processados automaticamente');
      console.log('✅ Use comandos para pagamentos manuais quando necessário');
      
    } else {
      console.log('❌ ⚠️ SISTEMA COM PROBLEMAS');
      console.log('');
      console.log(`📊 Status: ${statusData.status || 'ERRO'}`);
      console.log(`🚀 Webhook: ${webhookResult.success ? 'OK' : 'ERRO'}`);
      console.log(`🔔 Notificações: ${notificationResult.success ? 'OK' : 'ERRO'}`);
      
      console.log('\n🔧 VERIFICAR:');
      console.log('- Servidor Next.js rodando na porta 3000');
      console.log('- Banco de dados MySQL conectado');
      console.log('- Configurações no .env.local');
    }

  } catch (error) {
    console.error('❌ Erro no teste final:', error.message);
    console.log('\n🔧 VERIFICAR:');
    console.log('- Servidor Next.js está rodando?');
    console.log('- Porta 3000 está disponível?');
    console.log('- Banco de dados está conectado?');
  }

  console.log('\n' + '=' .repeat(60));
  console.log('🏁 Teste final concluído');
}

// Executar teste
testFinal().catch(console.error);
