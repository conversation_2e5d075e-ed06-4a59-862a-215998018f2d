import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/database-config'

// Forçar rota dinâmica
export const dynamic = 'force-dynamic'
export const runtime = 'nodejs'

const FOOTBALL_API_URL = process.env.FOOTBALL_API_URL || 'https://api.football-data.org/v4'
const FOOTBALL_API_TOKEN = process.env.FOOTBALL_API_TOKEN || 'cbeb9f19b15e4252b3f9d3375fefcfcc'

// Cache simples em memória com TTL
const cache = new Map()
const CACHE_DURATION = 10 * 60 * 1000 // 10 minutos
const REQUEST_DELAY = 1000 // 1 segundo entre requisições

// Rate limiting
let lastRequestTime = 0
let requestCount = 0
const MAX_REQUESTS_PER_MINUTE = 10

// Mapeamento de campeonatos para códigos da API
const COMPETITION_MAPPING: { [key: string]: string } = {
  'premier-league': 'PL',
  'la-liga': 'PD',
  'serie-a': 'SA',
  'bundesliga': 'BL1',
  'ligue-1': 'FL1',
  'champions-league': 'CL',
  'europa-league': 'EL'
}

async function delay(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms))
}

async function fetchFootballData(endpoint: string) {
  // Verificar cache primeiro
  const cacheKey = endpoint
  const cached = cache.get(cacheKey)
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    console.log(`📦 Cache hit: ${endpoint}`)
    return cached.data
  }

  // Rate limiting - verificar se excedeu limite
  const now = Date.now()
  if (requestCount >= MAX_REQUESTS_PER_MINUTE) {
    console.log(`⏳ Rate limit preventivo atingido, aguardando...`)
    await delay(60000) // Aguardar 1 minuto
    requestCount = 0
  }

  // Aguardar entre requisições
  const timeSinceLastRequest = now - lastRequestTime
  if (timeSinceLastRequest < REQUEST_DELAY) {
    await delay(REQUEST_DELAY - timeSinceLastRequest)
  }

  try {
    console.log(`🌐 Buscando dados: ${FOOTBALL_API_URL}${endpoint}`)

    const response = await fetch(`${FOOTBALL_API_URL}${endpoint}`, {
      headers: {
        'X-Auth-Token': FOOTBALL_API_TOKEN,
        'Content-Type': 'application/json'
      }
    })

    lastRequestTime = Date.now()
    requestCount++

    if (!response.ok) {
      if (response.status === 429) {
        console.log(`⏳ Rate limit da API atingido para ${endpoint}, aguardando...`)
        await delay(60000) // Aguardar 1 minuto
        requestCount = 0
        throw new Error(`HTTP 429: Rate limit exceeded`)
      }
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()

    // Salvar no cache
    cache.set(cacheKey, {
      data,
      timestamp: Date.now()
    })

    console.log(`✅ Dados recebidos: ${endpoint}`)
    return data
  } catch (error) {
    console.error(`❌ Erro ao buscar ${endpoint}:`, error)
    throw error
  }
}

function formatPartida(match: any, competitionInfo: any) {
  return {
    id: match.id,
    competition: competitionInfo.code || 'unknown',
    competitionName: competitionInfo.name || 'Competição',
    competitionLogo: competitionInfo.emblem || null,
    homeTeam: {
      id: match.homeTeam.id,
      name: match.homeTeam.name,
      shortName: match.homeTeam.shortName || match.homeTeam.tla || match.homeTeam.name,
      tla: match.homeTeam.tla,
      crest: match.homeTeam.crest || null
    },
    awayTeam: {
      id: match.awayTeam.id,
      name: match.awayTeam.name,
      shortName: match.awayTeam.shortName || match.awayTeam.tla || match.awayTeam.name,
      tla: match.awayTeam.tla,
      crest: match.awayTeam.crest || null
    },
    utcDate: match.utcDate,
    status: match.status,
    matchday: match.matchday,
    stage: match.stage,
    score: match.score,
    venue: match.venue || null,
    // Campos adicionais para compatibilidade
    data_jogo: match.utcDate,
    time_casa_nome: match.homeTeam.name,
    time_casa_logo: match.homeTeam.crest,
    time_fora_nome: match.awayTeam.name,
    time_fora_logo: match.awayTeam.crest,
    campeonato_nome: competitionInfo.name,
    campeonato_logo: competitionInfo.emblem
  }
}

export async function GET(request: NextRequest) {
  try {
    // Evitar execução durante build
    if (process.env.NEXT_PHASE === 'phase-production-build' ||
        process.env.NODE_ENV === 'production' && !request.headers.get('host') ||
        !request.url) {
      return NextResponse.json({ partidas: [], message: "Build mode" })
    }

    console.log("🔍 Buscando partidas...")

    // Proteção adicional para URL parsing
    let searchParams
    try {
      searchParams = new URL(request.url).searchParams
    } catch (urlError) {
      console.log("⚠️ Erro ao processar URL durante build, retornando dados vazios")
      return NextResponse.json({ partidas: [], message: "URL parsing error during build" })
    }
    const competitionsParam = searchParams.get('competitions')
    const dateFrom = searchParams.get('dateFrom') || new Date().toISOString().split('T')[0]
    const dateTo = searchParams.get('dateTo') || new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    const status = searchParams.get('status') || 'SCHEDULED,TIMED'
    const limit = parseInt(searchParams.get('limit') || '999')

    console.log('🏆 Buscando partidas reais da Football Data API:', {
      competitions: competitionsParam,
      dateFrom,
      dateTo,
      status,
      limit
    })

    // Se não especificar competições, buscar de todas as competições disponíveis
    let competitions: string[] = []

    if (!competitionsParam || competitionsParam === 'all') {
      // Buscar todas as competições disponíveis
      try {
        const allCompetitionsData = await fetchFootballData('/competitions')
        const allCompetitions = allCompetitionsData.competitions || []
        competitions = allCompetitions.map((comp: any) => comp.code).filter(Boolean)
        console.log(`🌍 Buscando partidas de TODAS as ${competitions.length} competições disponíveis`)
      } catch (error) {
        console.error('❌ Erro ao buscar todas as competições:', error)
        return NextResponse.json({
          success: false,
          error: 'Erro ao buscar competições disponíveis'
        }, { status: 500 })
      }
    } else {
      competitions = competitionsParam.split(',')
    }

    const allMatches: any[] = []
    const competitionData: any[] = []

    // Buscar partidas para cada competição
    for (const compName of competitions) {
      try {
        const compCode = COMPETITION_MAPPING[compName.toLowerCase()] || compName.toUpperCase()
        
        // Buscar informações da competição
        const competitionInfo = await fetchFootballData(`/competitions/${compCode}`)
        competitionData.push(competitionInfo)

        // Buscar partidas da competição
        const matchesData = await fetchFootballData(
          `/competitions/${compCode}/matches?dateFrom=${dateFrom}&dateTo=${dateTo}&status=${status}`
        )

        if (matchesData.matches && matchesData.matches.length > 0) {
          const formattedMatches = matchesData.matches
            .map((match: any) => formatPartida(match, competitionInfo))
          
          allMatches.push(...formattedMatches)
          console.log(`✅ ${formattedMatches.length} partidas encontradas para ${competitionInfo.name}`)
        }

        // Delay para evitar rate limit
        await new Promise(resolve => setTimeout(resolve, 200))

      } catch (error) {
        console.error(`❌ Erro ao buscar partidas da competição ${compName}:`, error)

        // Fallback: buscar dados do banco local
        try {
          console.log(`🔄 Tentando fallback para dados do banco para ${compName}`)
          const localMatches = await executeQuery(`
            SELECT
              j.*,
              tc.nome as time_casa_nome,
              tc.logo_url as time_casa_logo,
              tf.nome as time_fora_nome,
              tf.logo_url as time_fora_logo,
              c.nome as campeonato_nome,
              c.logo_url as campeonato_logo
            FROM jogos j
            JOIN times tc ON j.time_casa_id = tc.id
            JOIN times tf ON j.time_fora_id = tf.id
            JOIN campeonatos c ON j.campeonato_id = c.id
            WHERE c.nome LIKE ? AND j.data_jogo >= ? AND j.data_jogo <= ?
            ORDER BY j.data_jogo ASC
            LIMIT 20
          `, [`%${compName}%`, dateFrom, dateTo])

          if (localMatches.length > 0) {
            const formattedLocalMatches = localMatches.map((match: any) => ({
              id: match.id,
              competition: compName.toUpperCase(),
              competitionName: match.campeonato_nome,
              competitionLogo: match.campeonato_logo,
              homeTeam: {
                id: match.time_casa_id,
                name: match.time_casa_nome,
                shortName: match.time_casa_nome,
                tla: match.time_casa_nome.substring(0, 3).toUpperCase(),
                crest: match.time_casa_logo
              },
              awayTeam: {
                id: match.time_fora_id,
                name: match.time_fora_nome,
                shortName: match.time_fora_nome,
                tla: match.time_fora_nome.substring(0, 3).toUpperCase(),
                crest: match.time_fora_logo
              },
              utcDate: match.data_jogo,
              status: match.status?.toUpperCase() || 'SCHEDULED',
              matchday: match.rodada || 1,
              stage: 'REGULAR_SEASON',
              score: match.resultado_casa !== null ? {
                fullTime: {
                  home: match.resultado_casa,
                  away: match.resultado_fora
                }
              } : null
            }))

            allMatches.push(...formattedLocalMatches)
            console.log(`✅ ${formattedLocalMatches.length} partidas encontradas no banco para ${compName}`)
          }
        } catch (dbError) {
          console.error(`❌ Erro no fallback do banco para ${compName}:`, dbError)
        }
      }
    }

    // Ordenar partidas por data
    allMatches.sort((a, b) => new Date(a.utcDate).getTime() - new Date(b.utcDate).getTime())

    // Usar todas as partidas sem limite
    const limitedMatches = allMatches

    console.log(`🎯 Total de partidas encontradas: ${limitedMatches.length}`)

    return NextResponse.json({
      success: true,
      partidas: limitedMatches,
      competitions: competitionData,
      total: limitedMatches.length,
      filters: {
        competitions: competitions,
        dateFrom,
        dateTo,
        status,
        limit
      }
    })

  } catch (error) {
    console.error('❌ Erro ao buscar partidas:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Erro ao buscar partidas da API',
      message: error.message,
      partidas: [],
      competitions: [],
      total: 0
    }, { status: 500 })
  }
}
