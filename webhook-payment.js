#!/usr/bin/env node

// Simula webhook de pagamento real
import fetch from 'node-fetch';

async function webhookPayment() {
  const bilheteCode = process.argv[2];
  const status = process.argv[3] || 'PAID';

  if (!bilheteCode) {
    console.log('💳 WEBHOOK DE PAGAMENTO PIX');
    console.log('=' .repeat(40));
    console.log('');
    console.log('📝 USO:');
    console.log('  node webhook-payment.js <CODIGO_BILHETE> [STATUS]');
    console.log('');
    console.log('📋 EXEMPLOS:');
    console.log('  node webhook-payment.js BLT175312906921427FPGHXI PAID');
    console.log('  node webhook-payment.js BLT175312906921427FPGHXI PENDING');
    console.log('  node webhook-payment.js BLT175312906921427FPGHXI CANCELLED');
    console.log('');
    console.log('📊 STATUS DISPONÍVEIS:');
    console.log('  PAID      - Pagamento aprovado');
    console.log('  PENDING   - Pagamento pendente');
    console.log('  CANCELLED - Pagamento cancelado');
    console.log('');
    return;
  }

  console.log('💳 SIMULANDO WEBHOOK DE PAGAMENTO PIX');
  console.log('=' .repeat(45));
  console.log(`🎫 Bilhete: ${bilheteCode}`);
  console.log(`📊 Status: ${status}`);

  try {
    // 1. Buscar informações do bilhete
    console.log('\n🔍 1. Buscando informações do bilhete...');
    const bilhetesResponse = await fetch(`http://localhost:3000/api/user/bilhetes?user_id=27`);
    const bilhetesData = await bilhetesResponse.json();
    
    if (!bilhetesData.success) {
      console.log('❌ Erro ao buscar bilhetes:', bilhetesData.error);
      return;
    }

    const bilhete = bilhetesData.bilhetes.find(b => 
      b.codigo === bilheteCode || 
      b.transaction_id === bilheteCode
    );

    if (!bilhete) {
      console.log('❌ Bilhete não encontrado:', bilheteCode);
      console.log('\n📋 Bilhetes disponíveis:');
      bilhetesData.bilhetes.forEach(b => {
        console.log(`  - ${b.codigo} (${b.status}) - R$ ${b.valor_total}`);
      });
      return;
    }

    console.log(`✅ Bilhete encontrado: ${bilhete.codigo}`);
    console.log(`💰 Valor: R$ ${bilhete.valor_total}`);
    console.log(`📊 Status atual: ${bilhete.status}`);
    console.log(`🔗 Transaction ID: ${bilhete.transaction_id}`);

    // 2. Enviar webhook
    console.log('\n🔔 2. Enviando webhook...');
    const webhookResponse = await fetch('http://localhost:3000/api/v1/MP/webhookruntransation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'PIX-Webhook-Simulator/1.0',
        'X-Webhook-Source': 'payment-simulator'
      },
      body: JSON.stringify({
        order_id: bilhete.transaction_id || bilhete.codigo,
        status: status,
        type: 'PIXOUT',
        message: `Pagamento ${status.toLowerCase()} via PIX`,
        amount: parseFloat(bilhete.valor_total),
        payment_method: 'PIX',
        transaction_id: bilhete.transaction_id,
        bilhete_codigo: bilhete.codigo,
        timestamp: new Date().toISOString(),
        webhook_source: 'simulator'
      })
    });
    
    const webhookResult = await webhookResponse.json();
    
    if (webhookResult.success) {
      console.log('✅ WEBHOOK PROCESSADO COM SUCESSO!');
      console.log(`📊 Status anterior: ${webhookResult.bilhete_original?.status || 'N/A'}`);
      console.log(`📊 Status atual: ${webhookResult.bilhete_atualizado?.status || 'N/A'}`);
      console.log(`🔄 Linhas afetadas: ${webhookResult.linhas_afetadas || 0}`);
      
      if (webhookResult.bilhete_atualizado?.status === 'pago') {
        console.log('\n🎉 PAGAMENTO APROVADO!');
        console.log('✅ O modal de sucesso deve aparecer na interface');
        console.log('✅ Status atualizado no banco de dados');
      } else if (webhookResult.bilhete_atualizado?.status === 'cancelado') {
        console.log('\n🚫 PAGAMENTO CANCELADO!');
        console.log('⚠️ Bilhete marcado como cancelado');
      } else if (webhookResult.bilhete_atualizado?.status === 'pendente') {
        console.log('\n⏳ PAGAMENTO PENDENTE!');
        console.log('🔄 Aguardando confirmação do pagamento');
      }
      
    } else {
      console.log('❌ ERRO NO WEBHOOK:', webhookResult.message);
      console.log('🔧 Detalhes:', webhookResult.error || 'Erro desconhecido');
    }

    // 3. Verificar status atualizado
    console.log('\n🔍 3. Verificando status final...');
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const statusResponse = await fetch(`http://localhost:3000/api/pix/check-status?transaction_id=${bilhete.transaction_id}`);
    const statusData = await statusResponse.json();
    
    console.log(`📊 Status no sistema: ${statusData.status}`);
    console.log(`🕐 Última verificação: ${statusData.last_check || 'N/A'}`);

    // 4. Resultado final
    console.log('\n🎯 RESULTADO:');
    console.log('=' .repeat(25));
    
    if (webhookResult.success) {
      console.log('🎉 ✅ WEBHOOK FUNCIONOU!');
      
      if (status === 'PAID') {
        console.log('');
        console.log('💡 PRÓXIMOS PASSOS:');
        console.log('1. 🌐 Acesse http://localhost:3000');
        console.log('2. 🔑 Faça login se necessário');
        console.log('3. 🎉 Veja o modal de sucesso aparecer');
        console.log('4. 📊 Verifique o status do bilhete');
      }
      
    } else {
      console.log('❌ ⚠️ WEBHOOK FALHOU');
      console.log('🔧 Verifique os logs do servidor');
    }

  } catch (error) {
    console.error('❌ Erro:', error.message);
  }

  console.log('\n' + '=' .repeat(45));
  console.log('🏁 Simulação finalizada');
}

// Executar
webhookPayment().catch(console.error);
