#!/usr/bin/env node

// Smart Webhook Caller - <PERSON><PERSON> remoto primeiro, depois local
import fetch from 'node-fetch';

const WEBHOOK_URLS = {
  remote: 'https://ouroemu.site/api/v1/MP/webhookruntransation',
  local: 'http://localhost:3000/api/v1/MP/webhookruntransation'
};

async function callWebhook(url, data, timeout = 10000) {
  try {
    console.log(`🔗 Tentando webhook: ${url}`);
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Smart-Webhook-Caller/1.0'
      },
      body: JSON.stringify(data),
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    
    if (response.ok) {
      const result = await response.json();
      return { success: true, data: result, url };
    } else {
      return { 
        success: false, 
        error: `HTTP ${response.status}: ${response.statusText}`, 
        url 
      };
    }
    
  } catch (error) {
    return { 
      success: false, 
      error: error.message, 
      url 
    };
  }
}

async function smartWebhookCall(bilheteCodigo, paymentData = {}) {
  const webhookData = {
    order_id: bilheteCodigo,
    status: "PAID",
    type: "PIXOUT",
    message: "Payment approved",
    timestamp: new Date().toISOString(),
    ...paymentData
  };
  
  console.log(`🎯 Processando pagamento para: ${bilheteCodigo}`);
  console.log(`📦 Dados do webhook:`, webhookData);
  
  // Tentar webhook remoto primeiro
  console.log('\n🌐 Tentativa 1: Webhook Remoto');
  const remoteResult = await callWebhook(WEBHOOK_URLS.remote, webhookData, 8000);
  
  if (remoteResult.success) {
    console.log('✅ Webhook remoto funcionou!');
    console.log('📊 Resposta:', JSON.stringify(remoteResult.data, null, 2));
    return { success: true, method: 'remote', result: remoteResult.data };
  } else {
    console.log(`❌ Webhook remoto falhou: ${remoteResult.error}`);
  }
  
  // Tentar webhook local como fallback
  console.log('\n🏠 Tentativa 2: Webhook Local (Fallback)');
  const localResult = await callWebhook(WEBHOOK_URLS.local, webhookData, 5000);
  
  if (localResult.success) {
    console.log('✅ Webhook local funcionou!');
    console.log('📊 Resposta:', JSON.stringify(localResult.data, null, 2));
    return { success: true, method: 'local', result: localResult.data };
  } else {
    console.log(`❌ Webhook local falhou: ${localResult.error}`);
  }
  
  // Ambos falharam
  console.log('\n❌ ERRO: Ambos os webhooks falharam!');
  return { 
    success: false, 
    errors: {
      remote: remoteResult.error,
      local: localResult.error
    }
  };
}

async function verifyPaymentStatus(bilheteCodigo) {
  try {
    console.log(`\n🔍 Verificando status do bilhete: ${bilheteCodigo}`);
    
    const response = await fetch(`http://localhost:3000/api/debug-bilhete?codigo=${bilheteCodigo}`);
    const result = await response.json();
    
    if (result.success && result.bilhetes.length > 0) {
      const bilhete = result.bilhetes[0];
      console.log(`📊 Status atual: ${bilhete.status}`);
      console.log(`💰 Valor: R$ ${bilhete.valor}`);
      console.log(`👤 Usuário: ${bilhete.usuario_nome}`);
      console.log(`🕐 Última atualização: ${bilhete.updated_at}`);
      
      return bilhete.status === 'pago';
    } else {
      console.log('❌ Bilhete não encontrado');
      return false;
    }
  } catch (error) {
    console.error('❌ Erro ao verificar status:', error.message);
    return false;
  }
}

async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('❌ Uso: node smart-webhook-caller.js <CODIGO_BILHETE>');
    console.log('📝 Exemplo: node smart-webhook-caller.js BLT175312747595627AI4WKL');
    return;
  }
  
  const bilheteCodigo = args[0];
  
  console.log('🚀 Smart Webhook Caller Iniciado');
  console.log('=' .repeat(50));
  
  // Verificar status antes
  const statusAntes = await verifyPaymentStatus(bilheteCodigo);
  if (statusAntes) {
    console.log('ℹ️ Bilhete já está pago. Continuando mesmo assim...');
  }
  
  // Executar webhook inteligente
  console.log('\n🎯 Executando webhook inteligente...');
  const result = await smartWebhookCall(bilheteCodigo);
  
  if (result.success) {
    console.log(`\n🎉 SUCESSO! Pagamento processado via ${result.method}`);
    
    // Verificar se o status foi atualizado
    await new Promise(resolve => setTimeout(resolve, 1000)); // Aguardar 1 segundo
    const statusDepois = await verifyPaymentStatus(bilheteCodigo);
    
    if (statusDepois) {
      console.log('\n✅ CONFIRMADO: Status atualizado para PAGO! 🎉');
    } else {
      console.log('\n⚠️ ATENÇÃO: Webhook executou mas status não foi atualizado');
    }
  } else {
    console.log('\n❌ FALHA: Nenhum webhook funcionou');
    console.log('🔧 Erros encontrados:');
    console.log(`   Remoto: ${result.errors.remote}`);
    console.log(`   Local: ${result.errors.local}`);
    
    console.log('\n💡 Possíveis soluções:');
    console.log('   1. Verificar se o servidor local está rodando na porta 3000');
    console.log('   2. Verificar se o servidor remoto está funcionando');
    console.log('   3. Verificar conectividade de rede');
  }
  
  console.log('\n' + '=' .repeat(50));
  console.log('🏁 Smart Webhook Caller Finalizado');
}

// Executar
main().catch(console.error);
