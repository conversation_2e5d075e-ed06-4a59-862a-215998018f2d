#!/usr/bin/env node

/**
 * Script para configurar webhook na API meiodepagamento.com
 * 
 * Este script tenta configurar automaticamente o webhook para que
 * a API PIX envie notificações quando um pagamento for confirmado.
 */

import https from 'https'
import http from 'http'

// Configurações da API PIX
const PIX_API_URL = 'https://api.meiodepagamento.com/api/V1'
const PIX_TOKEN = 'Gk0e0W5bc2Guozw97NXv8duZbZCDbsedwMuTQNqqn5ZMT549Z0qDDZBF5ZbG9sHhXw9egbly3eiFJ4PKfs6ur+zzXs30gSDV6CYl8sQmSPJaISqpqf/FtJ2k30O3kO9ZsNwasalD0PGpD2wlAbb4ynO5S07P1kgWZRtw4w=='
const WEBHOOK_URL = 'https://ouroemu.site/api/v1/MP/webhookruntransation'

console.log('🔧 Configurando Webhook na API meiodepagamento.com...')
console.log('📍 API PIX:', PIX_API_URL)
console.log('🔗 Webhook URL:', WEBHOOK_URL)

async function configurarWebhook() {
  try {
    console.log('\n🔄 Iniciando configuração do webhook...')

    // 1. Testar conectividade com a API
    console.log('1️⃣ Testando conectividade com API PIX...')
    const apiTest = await testarAPI()
    
    if (apiTest.success) {
      console.log('✅ API PIX acessível')
    } else {
      console.log('⚠️ API PIX pode estar indisponível:', apiTest.error)
    }

    // 2. Tentar configurar webhook
    console.log('2️⃣ Tentando configurar webhook...')
    const configResult = await tentarConfigurarWebhook()
    
    if (configResult.success) {
      console.log('✅ Webhook configurado com sucesso!')
      console.log('📋 Detalhes:', configResult.details)
    } else {
      console.log('❌ Não foi possível configurar automaticamente')
      console.log('📖 Instruções manuais:')
      mostrarInstrucoesManuais()
    }

    // 3. Testar webhook
    console.log('3️⃣ Testando webhook...')
    const testResult = await testarWebhook()
    
    if (testResult.success) {
      console.log('✅ Webhook está funcionando!')
    } else {
      console.log('⚠️ Teste do webhook falhou:', testResult.error)
    }

    return configResult.success

  } catch (error) {
    console.error('❌ Erro geral:', error.message)
    return false
  }
}

async function testarAPI() {
  try {
    // Tentar fazer uma requisição simples para a API
    const response = await fazerRequisicao(`${PIX_API_URL}/status`, 'GET')
    return { success: true, response }
  } catch (error) {
    return { success: false, error: error.message }
  }
}

async function tentarConfigurarWebhook() {
  try {
    // Payload para configurar webhook
    const webhookConfig = {
      token: PIX_TOKEN,
      webhook_url: WEBHOOK_URL,
      events: ['payment.paid', 'payment.pending', 'payment.cancelled'],
      active: true,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    }

    // Endpoints possíveis para configuração de webhook
    const endpoints = [
      '/webhook/configure',
      '/webhook/config', 
      '/webhook/setup',
      '/config/webhook',
      '/settings/webhook',
      '/notification/webhook'
    ]

    for (const endpoint of endpoints) {
      try {
        console.log(`   🔄 Tentando: ${PIX_API_URL}${endpoint}`)
        
        const response = await fazerRequisicao(
          `${PIX_API_URL}${endpoint}`,
          'POST',
          webhookConfig
        )

        if (response.success || response.status === 'success' || response.message?.includes('success')) {
          return { 
            success: true, 
            details: { endpoint, response },
            message: `Webhook configurado via ${endpoint}`
          }
        }

      } catch (endpointError) {
        console.log(`   ❌ ${endpoint}: ${endpointError.message}`)
        continue
      }
    }

    return {
      success: false,
      error: 'Nenhum endpoint de configuração funcionou'
    }

  } catch (error) {
    return { success: false, error: error.message }
  }
}

async function testarWebhook() {
  try {
    const testPayload = {
      order_id: 'TEST_' + Date.now(),
      transaction_id: 'test_webhook_' + Date.now(),
      status: 'PAID',
      type: 'PIXOUT',
      message: 'Teste de configuração de webhook',
      amount: 0.01,
      source: 'webhook_config_test'
    }

    const response = await fazerRequisicao(
      WEBHOOK_URL,
      'POST',
      testPayload
    )

    return { success: true, response }

  } catch (error) {
    return { success: false, error: error.message }
  }
}

function mostrarInstrucoesManuais() {
  console.log('\n📖 INSTRUÇÕES PARA CONFIGURAÇÃO MANUAL:')
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
  console.log('1. Acesse o painel da meiodepagamento.com')
  console.log('2. Vá em "Configurações" ou "Webhooks"')
  console.log('3. Configure o webhook com os dados abaixo:')
  console.log('')
  console.log('   📍 URL do Webhook:')
  console.log(`   ${WEBHOOK_URL}`)
  console.log('')
  console.log('   🎯 Eventos para ativar:')
  console.log('   • payment.paid (Pagamento confirmado)')
  console.log('   • payment.pending (Pagamento pendente)')
  console.log('   • payment.cancelled (Pagamento cancelado)')
  console.log('')
  console.log('   🔧 Configurações:')
  console.log('   • Método: POST')
  console.log('   • Content-Type: application/json')
  console.log('   • Ativo: Sim')
  console.log('')
  console.log('   🔑 Token (se necessário):')
  console.log(`   ${PIX_TOKEN.substring(0, 20)}...`)
  console.log('')
  console.log('4. Salve as configurações')
  console.log('5. Teste fazendo um pagamento PIX')
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
}

function fazerRequisicao(url, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url)
    const isHttps = urlObj.protocol === 'https:'
    const client = isHttps ? https : http

    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Sistema-Bolao-Webhook-Config/1.0',
        'Accept': 'application/json'
      },
      timeout: 15000
    }

    if (data) {
      const postData = JSON.stringify(data)
      options.headers['Content-Length'] = Buffer.byteLength(postData)
    }

    const req = client.request(options, (res) => {
      let responseData = ''

      res.on('data', (chunk) => {
        responseData += chunk
      })

      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData)
          resolve(parsed)
        } catch (parseError) {
          resolve({ 
            status: res.statusCode, 
            data: responseData,
            headers: res.headers 
          })
        }
      })
    })

    req.on('error', (error) => {
      reject(error)
    })

    req.on('timeout', () => {
      req.destroy()
      reject(new Error('Request timeout'))
    })

    if (data) {
      req.write(JSON.stringify(data))
    }

    req.end()
  })
}

// Executar se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  configurarWebhook()
    .then(success => {
      if (success) {
        console.log('\n🎉 Configuração concluída!')
        console.log('💡 Agora os pagamentos PIX devem disparar o webhook automaticamente.')
        process.exit(0)
      } else {
        console.log('\n❌ Configuração automática falhou.')
        console.log('📖 Use as instruções manuais acima.')
        process.exit(1)
      }
    })
    .catch(error => {
      console.error('\n💥 Erro fatal:', error)
      process.exit(1)
    })
}

export { configurarWebhook }
