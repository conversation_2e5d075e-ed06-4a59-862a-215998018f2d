import { NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

const PIX_API_TOKEN = process.env.PIX_API_TOKEN || "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL2FwaS5tZWlvZGVwYWdhbWVudG8uY29tL2FwaS9hdXRoL2xvZ2luIiwiaWF0IjoxNzM3MjQ4NzE5LCJleHAiOjE3Mzc4NTM1MTksIm5iZiI6MTczNzI0ODcxOSwianRpIjoiZGNlZGNhNzNhNzNhNGJhNGJhNzNhNzNhNGJhNGJhNzMiLCJzdWIiOiI0NzMiLCJwcnYiOiIyM2JkNWM4OTQ5ZjYwMGFkYjM5ZTcwMWM0MDA4NzJkYjdhNTk3NmY3In0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8"

export async function POST(request: NextRequest) {
  try {
    console.log("🔄 Iniciando verificação automática de status PIX...")

    await initializeDatabase()

    // Buscar bilhetes pendentes dos últimos 2 dias
    const bilhetesPendentes = await executeQuery(`
      SELECT 
        id,
        codigo,
        transaction_id,
        valor_total,
        created_at,
        usuario_id
      FROM bilhetes 
      WHERE status = 'pendente' 
      AND created_at >= DATE_SUB(NOW(), INTERVAL 2 DAY)
      ORDER BY created_at DESC
      LIMIT 50
    `)

    console.log(`📊 Encontrados ${bilhetesPendentes.length} bilhetes pendentes para verificar`)

    if (bilhetesPendentes.length === 0) {
      return NextResponse.json({
        success: true,
        message: "Nenhum bilhete pendente encontrado",
        checked: 0,
        updated: 0
      })
    }

    let bilhetesAtualizados = 0
    const resultados = []

    // Verificar cada bilhete pendente
    for (const bilhete of bilhetesPendentes) {
      try {
        console.log(`🔍 Verificando bilhete: ${bilhete.codigo}`)

        // Usar smart webhook para verificar pagamento
        const response = await fetch(`${request.nextUrl.origin}/api/smart-webhook`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            order_id: bilhete.codigo,
            status: 'PAID',
            type: 'PIXOUT',
            message: 'Auto-check payment verification'
          })
        })

        if (response.ok) {
          const pixData = await response.json()
          console.log(`📊 Status PIX para ${bilhete.codigo}:`, pixData.status)

          // Se o status mudou para PAID, atualizar no banco
          if (pixData.status === 'PAID') {
            await executeQuery(`
              UPDATE bilhetes 
              SET status = 'pago', updated_at = NOW() 
              WHERE id = ?
            `, [bilhete.id])

            bilhetesAtualizados++
            
            resultados.push({
              codigo: bilhete.codigo,
              status_anterior: 'pendente',
              status_novo: 'pago',
              valor: parseFloat(bilhete.valor_total)
            })

            console.log(`✅ Bilhete ${bilhete.codigo} atualizado para PAGO`)
          } else {
            resultados.push({
              codigo: bilhete.codigo,
              status_anterior: 'pendente',
              status_novo: 'pendente',
              api_status: pixData.status
            })
          }
        } else if (response.status === 404) {
          console.log(`⚠️ Bilhete ${bilhete.codigo} não encontrado na API PIX`)
          resultados.push({
            codigo: bilhete.codigo,
            status_anterior: 'pendente',
            status_novo: 'pendente',
            api_status: 'NOT_FOUND'
          })
        } else {
          console.log(`❌ Erro ao consultar ${bilhete.codigo}: ${response.status}`)
        }

        // Delay para evitar rate limit
        await new Promise(resolve => setTimeout(resolve, 1000))

      } catch (error) {
        console.error(`❌ Erro ao verificar bilhete ${bilhete.codigo}:`, error)
        resultados.push({
          codigo: bilhete.codigo,
          status_anterior: 'pendente',
          status_novo: 'pendente',
          erro: error instanceof Error ? error.message : String(error)
        })
      }
    }

    console.log(`✅ Verificação concluída: ${bilhetesAtualizados} bilhetes atualizados`)

    return NextResponse.json({
      success: true,
      message: `Verificação automática concluída`,
      checked: bilhetesPendentes.length,
      updated: bilhetesAtualizados,
      resultados: resultados
    })

  } catch (error) {
    console.error("❌ Erro na verificação automática:", error)
    return NextResponse.json({
      error: "Erro na verificação automática",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: "Endpoint para verificação automática de status PIX",
    usage: "POST /api/pix/auto-check",
    description: "Verifica automaticamente o status de todos os bilhetes pendentes na API PIX"
  })
}
