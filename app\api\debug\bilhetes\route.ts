import { NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    await initializeDatabase()

    // Buscar todos os bilhetes recentes
    const bilhetes = await executeQuery(`
      SELECT 
        id, codigo, usuario_id, status, transaction_id, pix_order_id, created_at
      FROM bilhetes 
      ORDER BY created_at DESC 
      LIMIT 10
    `)

    console.log("🔍 Debug - Bilhetes encontrados:", bilhetes)

    return NextResponse.json({
      success: true,
      bilhetes: bilhetes,
      total: Array.isArray(bilhetes) ? bilhetes.length : 0
    })

  } catch (error) {
    console.error("❌ Erro no debug de bilhetes:", error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Erro desconhecido"
    }, { status: 500 })
  }
}
