// Biblioteca para integração com a API PIX - VERSÃO ATUALIZADA
const PIX_API_BASE_URL = process.env.PIX_API_URL || "https://joanadeoxum.com/api/v1/Transacao"
const PIX_TOKEN = process.env.PIX_API_TOKEN || "Gk0e0W5bc2Guozw97NXv8duZbZCDbsedwMuTQNqqn5ZMT549Z0qDDZBF5ZbG9sHhXw9egbly3eiFJ4PKfs6ur+zzXs30gSDV6CYl8sQmSPJaISqpqf/FtJ2k30O3kO9ZsNwasalD0PGpD2wlAbb4ynO5S07P1kgWZRtw4w=="

class PixAPI {
  constructor() {
    this.baseURL = PIX_API_BASE_URL
    this.token = PIX_TOKEN
  }

  async solicitarQRCode(dados) {
    try {
      const payload = {
        token: this.token,
        value: Number.parseFloat(dados.value),
        description: dados.description || "Pagamento Bolão",
        client_name: dados.client_name,
        client_email: dados.client_email,
        client_document: dados.client_document,
        qrcode_image: true, // Sempre gerar imagem do QR Code
      }

      console.log("🔄 Enviando solicitação PIX:", payload)

      const response = await fetch(`${this.baseURL}/SolicitacaoQRCode`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error("❌ Erro HTTP PIX:", response.status, errorText)
        throw new Error(`Erro HTTP: ${response.status} - ${errorText}`)
      }

      const result = await response.json()
      console.log("✅ Resposta PIX recebida:", result)

      return {
        qr_code_value: result.qr_code_value,
        qrcode_image: result.qrcode_image,
        expiration_datetime: result.expiration_datetime,
        status: result.status,
        transaction_id: result.transaction_id,
        order_id: result.order_id,
      }
    } catch (error) {
      console.error("❌ Erro ao solicitar QR Code PIX:", error)
      throw error
    }
  }

  async verificarStatusPagamento(transactionId) {
    try {
      // Esta função seria implementada conforme a documentação da API
      // Por enquanto, simularemos a verificação
      console.log("🔍 Verificando status do pagamento:", transactionId)

      // Simular verificação real
      return {
        status: "PAID",
        transaction_id: transactionId,
        amount: "25.00",
      }
    } catch (error) {
      console.error("❌ Erro ao verificar status:", error)
      throw error
    }
  }

  // Webhook handler para receber notificações de pagamento
  handleWebhook(webhookData) {
    /*
    Estrutura esperada do webhook conforme documentação:
    {
      "qr_code_payment_id": "99999999999",
      "transaction_id": "999999999",
      "order_id": "9999999",
      "amount": "0.01",
      "status": "PAID",
      "end_to_end_id": "2024-08-03 02:01:37.851955+00",
      "last_updated_at": "2024-08-03 02:01:37.851955+00",
      "error": null
    }
    */

    console.log("📨 Webhook PIX recebido:", webhookData)

    switch (webhookData.status) {
      case "PAID":
        console.log("✅ Pagamento confirmado!")
        return { success: true, message: "Pagamento confirmado" }
      case "PENDING":
        console.log("⏳ Pagamento pendente")
        return { success: false, message: "Pagamento pendente" }
      case "FAILED":
        console.log("❌ Pagamento falhou")
        return { success: false, message: "Pagamento falhou" }
      case "DECLINED":
        console.log("🚫 Pagamento recusado")
        return { success: false, message: "Pagamento recusado" }
      default:
        console.log("❓ Status desconhecido:", webhookData.status)
        return { success: false, message: "Status desconhecido" }
    }
  }
}

// Instância global da API PIX
const pixAPI = new PixAPI()

// Exportar para uso em outros módulos
if (typeof module !== "undefined" && module.exports) {
  module.exports = { PixAPI, pixAPI }
}

// Exemplo de uso real
async function exemploUsoReal() {
  try {
    console.log("🔄 Testando API PIX REAL...")

    // Solicitar QR Code real
    const qrCodeResponse = await pixAPI.solicitarQRCode({
      value: 25.0,
      description: "Bolão Brasil - 11 apostas",
      client_name: "João Silva",
      client_email: "<EMAIL>",
      client_document: "12345678901",
    })

    console.log("✅ QR Code PIX gerado:", qrCodeResponse)

    return qrCodeResponse
  } catch (error) {
    console.error("❌ Erro no teste PIX:", error)
    throw error
  }
}

// Função para configurações PIX do admin
function getPixConfig() {
  return {
    baseURL: PIX_API_BASE_URL,
    token: PIX_TOKEN,
    timeout: 15, // minutos
    commission: 10, // porcentagem
    autoConfirm: true,
  }
}

// Exportar configurações
if (typeof window !== "undefined") {
  window.pixAPI = pixAPI
  window.getPixConfig = getPixConfig
}
