#!/usr/bin/env node

/**
 * Script para configurar webhook na API PIX meiodepagamento.com
 * 
 * Este script configura automaticamente o webhook para que a API PIX
 * envie notificações para o seu sistema quando um pagamento for confirmado.
 */

const https = require('https')
const http = require('http')

// Configurações da API PIX
const PIX_API_URL = process.env.PIX_API_URL || 'https://api.meiodepagamento.com/api/V1'
const PIX_TOKEN = process.env.PIX_API_TOKEN || 'Gk0e0W5bc2Guozw97NXv8duZbZCDbsedwMuTQNqqn5ZMT549Z0qDDZBF5ZbG9sHhXw9egbly3eiFJ4PKfs6ur+zzXs30gSDV6CYl8sQmSPJaISqpqf/FtJ2k30O3kO9ZsNwasalD0PGpD2wlAbb4ynO5S07P1kgWZRtw4w=='
const WEBHOOK_URL = process.env.PIX_WEBHOOK_URL || 'https://ouroemu.site/api/v1/MP/webhookruntransation'

console.log('🔧 Configurando Webhook PIX...')
console.log('📍 API PIX:', PIX_API_URL)
console.log('🔗 Webhook URL:', WEBHOOK_URL)

async function configurarWebhook() {
  try {
    console.log('\n🔄 Iniciando configuração do webhook...')

    // 1. Verificar se a API PIX está acessível
    console.log('1️⃣ Verificando conectividade com API PIX...')
    const apiStatus = await verificarAPIPIX()
    
    if (!apiStatus.success) {
      console.error('❌ Erro ao conectar com API PIX:', apiStatus.error)
      return false
    }
    
    console.log('✅ API PIX acessível')

    // 2. Verificar se o webhook está acessível
    console.log('2️⃣ Verificando acessibilidade do webhook...')
    const webhookStatus = await verificarWebhook()
    
    if (!webhookStatus.success) {
      console.warn('⚠️ Webhook pode não estar acessível:', webhookStatus.error)
    } else {
      console.log('✅ Webhook acessível')
    }

    // 3. Configurar webhook na API PIX
    console.log('3️⃣ Configurando webhook na API PIX...')
    const configResult = await configurarWebhookNaAPI()
    
    if (configResult.success) {
      console.log('✅ Webhook configurado com sucesso!')
      console.log('📋 Detalhes:', configResult.details)
    } else {
      console.error('❌ Erro ao configurar webhook:', configResult.error)
      return false
    }

    // 4. Testar webhook
    console.log('4️⃣ Testando webhook...')
    const testResult = await testarWebhook()
    
    if (testResult.success) {
      console.log('✅ Teste do webhook bem-sucedido!')
    } else {
      console.warn('⚠️ Teste do webhook falhou:', testResult.error)
    }

    console.log('\n🎉 Configuração concluída!')
    console.log('📝 Resumo:')
    console.log(`   • API PIX: ${apiStatus.success ? '✅' : '❌'}`)
    console.log(`   • Webhook URL: ${webhookStatus.success ? '✅' : '⚠️'}`)
    console.log(`   • Configuração: ${configResult.success ? '✅' : '❌'}`)
    console.log(`   • Teste: ${testResult.success ? '✅' : '⚠️'}`)

    return true

  } catch (error) {
    console.error('❌ Erro geral na configuração:', error.message)
    return false
  }
}

async function verificarAPIPIX() {
  try {
    const response = await fazerRequisicao(PIX_API_URL, 'GET')
    return { success: true, response }
  } catch (error) {
    return { success: false, error: error.message }
  }
}

async function verificarWebhook() {
  try {
    const response = await fazerRequisicao(WEBHOOK_URL, 'GET')
    return { success: true, response }
  } catch (error) {
    return { success: false, error: error.message }
  }
}

async function configurarWebhookNaAPI() {
  try {
    // Payload para configurar webhook
    const payload = {
      token: PIX_TOKEN,
      webhook_url: WEBHOOK_URL,
      events: ['payment.paid', 'payment.pending', 'payment.cancelled'],
      active: true
    }

    // Tentar diferentes endpoints possíveis para configuração de webhook
    const endpoints = [
      '/ConfigurarWebhook',
      '/Webhook/Configure', 
      '/SetWebhook',
      '/webhook/config'
    ]

    for (const endpoint of endpoints) {
      try {
        console.log(`   🔄 Tentando endpoint: ${endpoint}`)
        
        const response = await fazerRequisicao(
          PIX_API_URL + endpoint,
          'POST',
          payload
        )

        if (response.success || response.status === 'success') {
          return { 
            success: true, 
            details: { endpoint, response },
            message: 'Webhook configurado via ' + endpoint
          }
        }

      } catch (endpointError) {
        console.log(`   ⚠️ Endpoint ${endpoint} não funcionou:`, endpointError.message)
        continue
      }
    }

    // Se nenhum endpoint funcionou, retornar instruções manuais
    return {
      success: false,
      error: 'Nenhum endpoint de configuração automática funcionou',
      instrucoes_manuais: {
        message: 'Configure manualmente no painel da API',
        webhook_url: WEBHOOK_URL,
        token: PIX_TOKEN,
        eventos: ['payment.paid', 'payment.pending', 'payment.cancelled']
      }
    }

  } catch (error) {
    return { success: false, error: error.message }
  }
}

async function testarWebhook() {
  try {
    // Payload de teste
    const testPayload = {
      order_id: 'TEST_' + Date.now(),
      transaction_id: 'test_' + Date.now(),
      status: 'PAID',
      type: 'PIXOUT',
      message: 'Teste de configuração de webhook',
      amount: 0.01,
      source: 'webhook_config_test'
    }

    const response = await fazerRequisicao(
      WEBHOOK_URL,
      'POST',
      testPayload
    )

    return { success: true, response }

  } catch (error) {
    return { success: false, error: error.message }
  }
}

function fazerRequisicao(url, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url)
    const isHttps = urlObj.protocol === 'https:'
    const client = isHttps ? https : http

    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Sistema-Bolao-Webhook-Config/1.0'
      },
      timeout: 15000
    }

    if (data) {
      const postData = JSON.stringify(data)
      options.headers['Content-Length'] = Buffer.byteLength(postData)
    }

    const req = client.request(options, (res) => {
      let responseData = ''

      res.on('data', (chunk) => {
        responseData += chunk
      })

      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData)
          resolve(parsed)
        } catch (parseError) {
          resolve({ 
            status: res.statusCode, 
            data: responseData,
            headers: res.headers 
          })
        }
      })
    })

    req.on('error', (error) => {
      reject(error)
    })

    req.on('timeout', () => {
      req.destroy()
      reject(new Error('Request timeout'))
    })

    if (data) {
      req.write(JSON.stringify(data))
    }

    req.end()
  })
}

// Executar se chamado diretamente
if (require.main === module) {
  configurarWebhook()
    .then(success => {
      if (success) {
        console.log('\n🎉 Configuração concluída com sucesso!')
        console.log('💡 Agora os pagamentos PIX devem disparar o webhook automaticamente.')
        process.exit(0)
      } else {
        console.log('\n❌ Configuração falhou.')
        console.log('📖 Consulte a documentação para configuração manual.')
        process.exit(1)
      }
    })
    .catch(error => {
      console.error('\n💥 Erro fatal:', error)
      process.exit(1)
    })
}

module.exports = { configurarWebhook }
