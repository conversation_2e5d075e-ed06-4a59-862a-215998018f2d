import { NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

// Sistema de Webhook Automático - Monitora e processa pagamentos PIX
export async function POST(request: NextRequest) {
  try {
    console.log("🤖 Sistema de Webhook Automático iniciado")
    
    await initializeDatabase()

    // Buscar bilhetes pendentes dos últimos 30 minutos
    const bilhetesPendentes = await executeQuery(`
      SELECT 
        id,
        codigo,
        transaction_id,
        pix_order_id,
        valor_total,
        created_at,
        usuario_id,
        status
      FROM bilhetes 
      WHERE status = 'pendente' 
      AND created_at >= DATE_SUB(NOW(), INTERVAL 30 MINUTE)
      ORDER BY created_at DESC
      LIMIT 20
    `)

    console.log(`📊 Encontrados ${bilhetesPendentes.length} bilhetes pendentes para verificar`)

    if (bilhetesPendentes.length === 0) {
      return NextResponse.json({
        success: true,
        message: "Nenhum bilhete pendente encontrado",
        checked: 0,
        processed: 0
      })
    }

    let bilhetesProcessados = 0
    const resultados = []

    // URLs dos webhooks
    const WEBHOOK_URLS = {
      remote: 'https://ouroemu.site/api/v1/MP/webhookruntransation',
      local: `${request.nextUrl.origin}/api/v1/MP/webhookruntransation`,
      smart: `${request.nextUrl.origin}/api/smart-webhook`
    }

    // Função para simular verificação de pagamento PIX
    async function verificarPagamentoPIX(bilhete: any) {
      // Aqui você pode implementar a lógica real de verificação
      // Por enquanto, vamos simular que alguns pagamentos foram aprovados
      
      // Simular que bilhetes criados há mais de 2 minutos foram "pagos"
      const agora = new Date()
      const criadoEm = new Date(bilhete.created_at)
      const diferencaMinutos = (agora.getTime() - criadoEm.getTime()) / (1000 * 60)
      
      // Para demonstração: considerar "pago" se foi criado há mais de 2 minutos
      // Na produção, você faria uma consulta real à API PIX
      return diferencaMinutos > 2
    }

    // Função para chamar webhook inteligente
    async function chamarWebhookInteligente(bilhete: any) {
      try {
        console.log(`🔗 Chamando webhook inteligente para: ${bilhete.codigo}`)
        
        const response = await fetch(WEBHOOK_URLS.smart, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            order_id: bilhete.codigo,
            status: 'PAID',
            type: 'PIXOUT',
            message: 'Payment approved via auto-webhook',
            transaction_id: bilhete.transaction_id,
            pix_order_id: bilhete.pix_order_id,
            valor: parseFloat(bilhete.valor_total)
          })
        })

        if (response.ok) {
          const result = await response.json()
          console.log(`✅ Webhook executado com sucesso para ${bilhete.codigo}`)
          return { success: true, data: result }
        } else {
          console.log(`❌ Webhook falhou para ${bilhete.codigo}: ${response.status}`)
          return { success: false, error: `HTTP ${response.status}` }
        }
      } catch (error: any) {
        console.log(`❌ Erro no webhook para ${bilhete.codigo}: ${error.message}`)
        return { success: false, error: error.message }
      }
    }

    // Processar cada bilhete pendente
    for (const bilhete of bilhetesPendentes) {
      try {
        console.log(`🔍 Verificando bilhete: ${bilhete.codigo}`)

        // Verificar se o pagamento foi aprovado
        const pagamentoAprovado = await verificarPagamentoPIX(bilhete)

        if (pagamentoAprovado) {
          console.log(`💳 Pagamento detectado para: ${bilhete.codigo}`)
          
          // Chamar webhook inteligente
          const webhookResult = await chamarWebhookInteligente(bilhete)
          
          if (webhookResult.success) {
            bilhetesProcessados++
            resultados.push({
              bilhete: bilhete.codigo,
              status: 'processed',
              method: webhookResult.data?.method || 'unknown',
              message: 'Pagamento processado automaticamente'
            })
          } else {
            resultados.push({
              bilhete: bilhete.codigo,
              status: 'failed',
              error: webhookResult.error,
              message: 'Falha ao processar webhook'
            })
          }
        } else {
          console.log(`⏳ Pagamento ainda pendente para: ${bilhete.codigo}`)
          resultados.push({
            bilhete: bilhete.codigo,
            status: 'pending',
            message: 'Aguardando pagamento'
          })
        }

      } catch (error: any) {
        console.error(`❌ Erro ao processar bilhete ${bilhete.codigo}:`, error)
        resultados.push({
          bilhete: bilhete.codigo,
          status: 'error',
          error: error.message
        })
      }
    }

    return NextResponse.json({
      success: true,
      message: `Sistema automático executado - ${bilhetesProcessados} pagamentos processados`,
      checked: bilhetesPendentes.length,
      processed: bilhetesProcessados,
      resultados,
      webhook_urls: WEBHOOK_URLS,
      timestamp: new Date().toISOString()
    })

  } catch (error: any) {
    console.error('❌ Erro no sistema de webhook automático:', error)
    return NextResponse.json({
      success: false,
      message: 'Erro no sistema automático',
      error: error.message
    }, { status: 500 })
  }
}

// Método GET para status e configuração
export async function GET(request: NextRequest) {
  try {
    await initializeDatabase()

    // Buscar estatísticas
    const stats = await executeQuery(`
      SELECT 
        COUNT(*) as total_bilhetes,
        SUM(CASE WHEN status = 'pendente' THEN 1 ELSE 0 END) as pendentes,
        SUM(CASE WHEN status = 'pago' THEN 1 ELSE 0 END) as pagos,
        SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 1 ELSE 0 END) as ultima_hora
      FROM bilhetes
    `)

    return NextResponse.json({
      message: "Sistema de Webhook Automático",
      status: "ativo",
      estatisticas: stats[0],
      configuracao: {
        intervalo_verificacao: "A cada requisição",
        tempo_limite: "30 minutos",
        bilhetes_por_execucao: 20
      },
      endpoints: {
        executar: "POST /api/auto-webhook",
        status: "GET /api/auto-webhook",
        webhook_inteligente: "/api/smart-webhook"
      },
      instrucoes: [
        "1. Execute POST /api/auto-webhook para verificar pagamentos",
        "2. O sistema verifica bilhetes pendentes dos últimos 30 minutos",
        "3. Pagamentos detectados são processados automaticamente",
        "4. Use GET /api/auto-webhook para ver estatísticas"
      ]
    })

  } catch (error: any) {
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 })
  }
}
