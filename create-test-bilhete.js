#!/usr/bin/env node

// Script para criar um bilhete de teste
import { executeQuery, initializeDatabase } from './lib/database-config.js';

async function createTestBilhete() {
  try {
    console.log('🎫 Criando bilhete de teste...\n');
    
    await initializeDatabase();
    
    const testBilhete = {
      codigo: 'BLT175312542333427L1NORV',
      usuario_id: 27,
      usuario_nome: 'mma mma',
      usuario_email: '<EMAIL>',
      usuario_cpf: '995.445.230-30',
      valor_total: 0.13,
      quantidade_apostas: 11,
      status: 'pendente',
      transaction_id: 'pixi_01k0q6nx5reahs9wnyerszakmq',
      qr_code_pix: '00020126810014br.gov.bcb.pix2559qr-code.picpay.com/pix/e348499c-7843-4c46-980f-77a6f637e4845204000053039865802BR5916DLM TECNOLOGIA E6009Sao Paulo62070503***63047C55'
    };
    
    console.log('📊 Dados do bilhete de teste:', testBilhete);
    
    // Tentar inserir o bilhete
    try {
      const result = await executeQuery(`
        INSERT INTO bilhetes (
          codigo, usuario_id, usuario_nome, usuario_email, usuario_cpf,
          valor_total, quantidade_apostas, status, transaction_id, qr_code_pix
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        testBilhete.codigo,
        testBilhete.usuario_id,
        testBilhete.usuario_nome,
        testBilhete.usuario_email,
        testBilhete.usuario_cpf,
        testBilhete.valor_total,
        testBilhete.quantidade_apostas,
        testBilhete.status,
        testBilhete.transaction_id,
        testBilhete.qr_code_pix
      ]);
      
      console.log('✅ Bilhete inserido com sucesso:', result);
      
      // Verificar se foi inserido
      const bilheteVerificacao = await executeQuery(`
        SELECT * FROM bilhetes WHERE codigo = ?
      `, [testBilhete.codigo]);
      
      if (bilheteVerificacao.length > 0) {
        console.log('✅ Bilhete verificado no banco:', bilheteVerificacao[0]);
      } else {
        console.log('❌ Bilhete não encontrado após inserção');
      }
      
    } catch (insertError) {
      console.error('❌ Erro ao inserir bilhete:', insertError);
      
      // Verificar se a tabela existe
      try {
        const tableInfo = await executeQuery('DESCRIBE bilhetes');
        console.log('📋 Estrutura da tabela bilhetes:');
        tableInfo.forEach(col => {
          console.log(`   ${col.Field}: ${col.Type} ${col.Null === 'YES' ? '(NULL)' : '(NOT NULL)'}`);
        });
      } catch (describeError) {
        console.error('❌ Erro ao verificar estrutura da tabela:', describeError);
      }
    }
    
  } catch (error) {
    console.error('❌ Erro geral:', error);
  }
}

// Executar criação
createTestBilhete().catch(console.error);
