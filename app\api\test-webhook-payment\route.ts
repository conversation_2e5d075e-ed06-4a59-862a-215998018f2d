import { NextRequest, NextResponse } from "next/server"
import { executeQuery, initializeDatabase } from "@/lib/database-config"

// Força renderização dinâmica para evitar erro de build estático
export const dynamic = 'force-dynamic'

export async function POST(request: NextRequest) {
  try {
    console.log("🧪 SIMULANDO WEBHOOK DE PAGAMENTO PIX")
    console.log("=" .repeat(50))
    
    const body = await request.json()
    const { transaction_id, bilhete_codigo } = body
    
    if (!transaction_id && !bilhete_codigo) {
      return NextResponse.json({
        success: false,
        error: "transaction_id ou bilhete_codigo é obrigatório"
      }, { status: 400 })
    }
    
    await initializeDatabase()
    
    // Buscar bilhete
    let bilhetes = []
    if (transaction_id) {
      bilhetes = await executeQuery(`
        SELECT * FROM bilhetes 
        WHERE transaction_id = ? 
        LIMIT 1
      `, [transaction_id])
    } else if (bilhete_codigo) {
      bilhetes = await executeQuery(`
        SELECT * FROM bilhetes 
        WHERE codigo = ? 
        LIMIT 1
      `, [bilhete_codigo])
    }
    
    if (bilhetes.length === 0) {
      return NextResponse.json({
        success: false,
        error: "Bilhete não encontrado",
        searched_for: transaction_id || bilhete_codigo
      }, { status: 404 })
    }
    
    const bilhete = bilhetes[0]
    console.log("📋 Bilhete encontrado:", {
      codigo: bilhete.codigo,
      transaction_id: bilhete.transaction_id,
      status_atual: bilhete.status,
      valor: bilhete.valor_total
    })
    
    // Simular dados do webhook
    const webhookData = {
      qr_code_payment_id: bilhete.transaction_id,
      transaction_id: bilhete.transaction_id,
      order_id: bilhete.pix_order_id || `ORDER_${bilhete.id}`,
      amount: parseFloat(bilhete.valor_total),
      description: `Pagamento bilhete ${bilhete.codigo}`,
      status: 'PAID',
      end_to_end_id: `E${Date.now()}${Math.random().toString(36).substr(2, 9)}`,
      last_updated_at: new Date().toISOString(),
      error: null
    }
    
    console.log("📡 Simulando webhook com dados:", webhookData)
    
    // Atualizar status do bilhete para PAGO
    if (bilhete.status !== 'pago') {
      await executeQuery(`
        UPDATE bilhetes 
        SET status = 'pago', updated_at = NOW() 
        WHERE id = ?
      `, [bilhete.id])
      
      console.log("✅ Status do bilhete atualizado para PAGO")
    } else {
      console.log("ℹ️ Bilhete já estava pago")
    }
    
    // Registrar log do webhook simulado
    try {
      await executeQuery(`
        INSERT INTO webhook_logs (
          transaction_id, 
          webhook_data, 
          status, 
          processed_at,
          source
        ) VALUES (?, ?, 'success', NOW(), 'simulator')
      `, [
        bilhete.transaction_id,
        JSON.stringify(webhookData)
      ])
      
      console.log("📝 Log do webhook simulado registrado")
    } catch (logError) {
      console.log("⚠️ Erro ao registrar log (não crítico):", logError)
    }
    
    // Buscar bilhete atualizado
    const bilheteAtualizado = await executeQuery(`
      SELECT * FROM bilhetes WHERE id = ?
    `, [bilhete.id])
    
    console.log("🎉 PAGAMENTO SIMULADO COM SUCESSO!")
    console.log("=" .repeat(50))
    
    return NextResponse.json({
      success: true,
      message: "Pagamento PIX simulado com sucesso",
      webhook_simulado: webhookData,
      bilhete_antes: {
        codigo: bilhete.codigo,
        status: bilhete.status,
        valor: parseFloat(bilhete.valor_total)
      },
      bilhete_depois: {
        codigo: bilheteAtualizado[0].codigo,
        status: bilheteAtualizado[0].status,
        valor: parseFloat(bilheteAtualizado[0].valor_total)
      },
      instrucoes: [
        "✅ Pagamento foi simulado com sucesso",
        "✅ Status do bilhete atualizado para 'pago'",
        "🔄 Recarregue a página para ver o modal de sucesso",
        "🎉 O sistema detectará automaticamente a mudança"
      ],
      proximos_passos: [
        "1. Recarregue a página da aplicação",
        "2. O modal de sucesso deve aparecer automaticamente",
        "3. O bilhete aparecerá como 'pago' na lista"
      ]
    })
    
  } catch (error) {
    console.error("❌ Erro ao simular pagamento:", error)
    return NextResponse.json({
      success: false,
      error: "Erro interno do servidor",
      message: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    return NextResponse.json({
      success: true,
      message: "Simulador de Webhook PIX",
      como_usar: {
        metodo: "POST",
        url: "/api/test-webhook-payment",
        body_opcao_1: {
          transaction_id: "pixi_01k0qtks92e7ctpm2q7hr57c1d"
        },
        body_opcao_2: {
          bilhete_codigo: "BLT17531463258451MRMRJR"
        }
      },
      exemplo_curl: [
        "curl -X POST http://localhost:3000/api/test-webhook-payment \\",
        "  -H 'Content-Type: application/json' \\",
        "  -d '{\"transaction_id\":\"pixi_01k0qtks92e7ctpm2q7hr57c1d\"}'"
      ],
      instrucoes: [
        "1. Use este endpoint para simular pagamentos PIX",
        "2. Forneça transaction_id OU bilhete_codigo",
        "3. O sistema atualizará o status para 'pago'",
        "4. Recarregue a página para ver o resultado"
      ]
    })
    
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: "Erro interno do servidor"
    }, { status: 500 })
  }
}
