import { NextRequest, NextResponse } from 'next/server'
import { executeQuery, initializeDatabase } from '@/lib/database-config'

export async function POST(request: NextRequest) {
  try {
    await initializeDatabase()
    
    const body = await request.json()
    const { transaction_id, status = 'PAID' } = body
    
    console.log("🧪 Teste de webhook - Dados recebidos:", { transaction_id, status })
    
    if (!transaction_id) {
      return NextResponse.json({ 
        error: "transaction_id é obrigatório" 
      }, { status: 400 })
    }
    
    // Buscar bilhete pelo transaction_id
    const bilhetes = await executeQuery(`
      SELECT * FROM bilhetes 
      WHERE transaction_id = ?
      LIMIT 1
    `, [transaction_id])
    
    console.log("🔍 Bilhetes encontrados:", bilhetes.length)
    
    if (bilhetes.length === 0) {
      // Buscar todos os bilhetes recentes para debug
      const bilhetesRecentes = await executeQuery(`
        SELECT codigo, transaction_id, status, valor_total, created_at 
        FROM bilhetes 
        ORDER BY created_at DESC 
        LIMIT 5
      `)
      
      return NextResponse.json({
        error: "Bilhete não encontrado",
        transaction_id_buscado: transaction_id,
        bilhetes_recentes: bilhetesRecentes.map(b => ({
          codigo: b.codigo,
          transaction_id: b.transaction_id,
          status: b.status,
          valor: b.valor_total,
          data: b.created_at
        }))
      }, { status: 404 })
    }
    
    const bilhete = bilhetes[0]
    console.log("✅ Bilhete encontrado:", {
      id: bilhete.id,
      codigo: bilhete.codigo,
      transaction_id: bilhete.transaction_id,
      status_atual: bilhete.status
    })
    
    // Mapear status
    let novoStatus = 'pendente'
    if (status === 'PAID' || status === 'paid' || status === 'aprovado') {
      novoStatus = 'pago'
    }
    
    // Atualizar status do bilhete
    const updateResult = await executeQuery(`
      UPDATE bilhetes 
      SET status = ?, updated_at = NOW() 
      WHERE id = ?
    `, [novoStatus, bilhete.id])
    
    console.log("✅ Bilhete atualizado:", updateResult)
    
    return NextResponse.json({
      success: true,
      message: "Webhook de teste processado com sucesso",
      bilhete: {
        id: bilhete.id,
        codigo: bilhete.codigo,
        transaction_id: bilhete.transaction_id,
        status_anterior: bilhete.status,
        status_novo: novoStatus,
        valor: bilhete.valor_total
      }
    })
    
  } catch (error) {
    console.error("❌ Erro no teste de webhook:", error)
    return NextResponse.json({
      error: "Erro interno do servidor",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}

export async function GET() {
  try {
    await initializeDatabase()
    
    // Buscar bilhetes recentes para debug
    const bilhetes = await executeQuery(`
      SELECT codigo, transaction_id, status, valor_total, created_at 
      FROM bilhetes 
      ORDER BY created_at DESC 
      LIMIT 10
    `)
    
    return NextResponse.json({
      message: "Endpoint de teste de webhook ativo",
      bilhetes_recentes: bilhetes.map(b => ({
        codigo: b.codigo,
        transaction_id: b.transaction_id,
        status: b.status,
        valor: b.valor_total,
        data: b.created_at
      })),
      instrucoes: {
        teste_webhook: "POST /api/test-webhook",
        payload: {
          transaction_id: "pix1_010f67c1f9e59p2eqcv59",
          status: "PAID"
        }
      }
    })
    
  } catch (error) {
    return NextResponse.json({
      error: "Erro ao buscar dados",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}
