#!/usr/bin/env node

// Teste completo do fluxo de pagamento automático
import fetch from 'node-fetch';

async function testCompleteFlow() {
  console.log('🧪 TESTE COMPLETO DO FLUXO DE PAGAMENTO AUTOMÁTICO');
  console.log('=' .repeat(60));

  try {
    // 1. Verificar bilhetes pendentes
    console.log('\n📊 1. Verificando bilhetes pendentes...');
    const pendentesResponse = await fetch('http://localhost:3000/api/simulate-pix-payment');
    const pendentesData = await pendentesResponse.json();
    
    console.log(`📋 Bilhetes pendentes encontrados: ${pendentesData.bilhetes_pendentes}`);
    
    if (pendentesData.bilhetes_pendentes === 0) {
      console.log('\n⚠️ Nenhum bilhete pendente encontrado para testar');
      console.log('💡 Para criar um bilhete:');
      console.log('   1. Acesse http://localhost:3000');
      console.log('   2. Faça login com: <EMAIL> / admin123');
      console.log('   3. Crie um bilhete');
      console.log('   4. Execute este teste novamente');
      return;
    }

    const bilhete = pendentesData.bilhetes[0];
    console.log(`\n🎫 Testando com bilhete: ${bilhete.codigo}`);
    console.log(`💰 Valor: R$ ${bilhete.valor}`);
    console.log(`🕐 Criado: ${new Date(bilhete.created_at).toLocaleString('pt-BR')}`);

    // 2. Verificar status inicial
    console.log('\n🔍 2. Verificando status inicial...');
    const statusResponse = await fetch(`http://localhost:3000/api/pix/check-status?transaction_id=${bilhete.transaction_id}`);
    const statusData = await statusResponse.json();
    
    console.log(`📊 Status inicial: ${statusData.status}`);
    console.log(`🔄 Última verificação: ${statusData.last_check}`);

    // 3. Simular pagamento PIX
    console.log('\n💳 3. Simulando pagamento PIX...');
    const paymentResponse = await fetch('http://localhost:3000/api/simulate-pix-payment', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        bilhete_codigo: bilhete.codigo,
        transaction_id: bilhete.transaction_id,
        valor: bilhete.valor
      })
    });
    
    const paymentResult = await paymentResponse.json();
    
    if (paymentResult.success) {
      console.log('✅ Pagamento simulado com sucesso!');
      console.log(`📊 Status anterior: ${paymentResult.bilhete_original.status}`);
      console.log(`📊 Status atual: ${paymentResult.bilhete_atualizado?.status || 'N/A'}`);
      console.log(`🔗 Método webhook: ${paymentResult.webhook_result?.method || 'N/A'}`);
      
      if (paymentResult.webhook_result?.fallback_reason) {
        console.log(`⚠️ Fallback usado: ${paymentResult.webhook_result.fallback_reason}`);
      }
      
    } else {
      console.log('❌ Erro na simulação:', paymentResult.message);
      return;
    }

    // 4. Verificar se o status foi atualizado
    console.log('\n🔄 4. Verificando atualização do status...');
    await new Promise(resolve => setTimeout(resolve, 1000)); // Aguardar 1 segundo
    
    const statusFinalResponse = await fetch(`http://localhost:3000/api/pix/check-status?transaction_id=${bilhete.transaction_id}`);
    const statusFinalData = await statusFinalResponse.json();
    
    console.log(`📊 Status final: ${statusFinalData.status}`);
    console.log(`🔄 Atualizado: ${statusFinalData.updated ? 'SIM' : 'NÃO'}`);

    // 5. Verificar bilhetes do usuário
    console.log('\n👤 5. Verificando bilhetes do usuário...');
    const userBilhetesResponse = await fetch(`http://localhost:3000/api/user/bilhetes?user_id=27`);
    const userBilhetesData = await userBilhetesResponse.json();
    
    if (userBilhetesData.success) {
      const bilheteAtualizado = userBilhetesData.bilhetes.find(b => b.codigo === bilhete.codigo);
      if (bilheteAtualizado) {
        console.log(`📊 Status no banco: ${bilheteAtualizado.status}`);
        console.log(`🕐 Última atualização: ${bilheteAtualizado.updated_at || 'N/A'}`);
      }
    }

    // 6. Testar sistema automático
    console.log('\n🤖 6. Testando sistema automático...');
    const autoResponse = await fetch('http://localhost:3000/api/auto-webhook', {
      method: 'POST'
    });
    const autoData = await autoResponse.json();
    
    console.log(`📊 Bilhetes verificados: ${autoData.checked}`);
    console.log(`✅ Bilhetes processados: ${autoData.processed}`);

    // 7. Verificar estatísticas finais
    console.log('\n📈 7. Estatísticas finais...');
    const statsResponse = await fetch('http://localhost:3000/api/auto-webhook');
    const statsData = await statsResponse.json();
    
    console.log(`📊 Total de bilhetes: ${statsData.estatisticas.total_bilhetes}`);
    console.log(`💳 Bilhetes pagos: ${statsData.estatisticas.pagos}`);
    console.log(`⏳ Bilhetes pendentes: ${statsData.estatisticas.pendentes}`);
    console.log(`🕐 Última hora: ${statsData.estatisticas.ultima_hora}`);

    // 8. Resultado final
    console.log('\n🎯 RESULTADO FINAL:');
    
    if (paymentResult.bilhete_atualizado?.status === 'pago') {
      console.log('🎉 ✅ TESTE PASSOU! Sistema funcionando 100%');
      console.log('   ✅ Pagamento PIX detectado');
      console.log('   ✅ Smart webhook executado');
      console.log('   ✅ Bilhete atualizado para PAGO');
      console.log('   ✅ Sistema automático funcionando!');
      
      console.log('\n🚀 PRÓXIMOS PASSOS:');
      console.log('   1. O sistema está funcionando automaticamente');
      console.log('   2. Quando criar novos bilhetes, eles serão processados automaticamente');
      console.log('   3. O modal "PAGO COM SUCESSO!" deve aparecer na interface');
      console.log('   4. Use os comandos para pagamentos manuais quando necessário');
      
    } else {
      console.log('❌ ⚠️ TESTE FALHOU! Verificar problemas:');
      console.log('   - Webhook pode não estar funcionando');
      console.log('   - Banco de dados pode ter problemas');
      console.log('   - API PIX pode estar indisponível');
    }

  } catch (error) {
    console.error('❌ Erro no teste:', error.message);
  }

  console.log('\n' + '=' .repeat(60));
  console.log('🏁 Teste finalizado');
}

// Executar teste
testCompleteFlow().catch(console.error);
