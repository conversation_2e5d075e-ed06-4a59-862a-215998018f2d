import { type NextRequest, NextResponse } from "next/server"
import { executeQuery } from "@/lib/database-config"

// Força renderização dinâmica para evitar erro de build estático
export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    console.log("📋 Listando todos os bolões...")

    // Buscar todos os bolões
    const boloes = await executeQuery(`
      SELECT 
        id,
        nome,
        descricao,
        valor_aposta,
        data_inicio,
        data_fim,
        status,
        campeonato_id,
        created_by,
        created_at,
        updated_at
      FROM boloes 
      ORDER BY created_at DESC
    `)

    console.log(`📊 Encontrados ${boloes.length} bolões`)

    // Buscar estatísticas para cada bolão
    const boloesComStats = await Promise.all(
      boloes.map(async (bolao: any) => {
        try {
          // Contar bilhetes
          const bilhetes = await executeQuery(
            'SELECT COUNT(*) as total FROM bilhetes WHERE bolao_id = ?',
            [bolao.id]
          )

          // Contar apostas
          const apostas = await executeQuery(`
            SELECT COUNT(*) as total 
            FROM apostas a 
            JOIN bilhetes b ON a.bilhete_id = b.id 
            WHERE b.bolao_id = ?
          `, [bolao.id])

          // Contar jogos
          const jogos = await executeQuery(
            'SELECT COUNT(*) as total FROM jogos WHERE bolao_id = ?',
            [bolao.id]
          )

          return {
            ...bolao,
            stats: {
              total_bilhetes: bilhetes[0]?.total || 0,
              total_apostas: apostas[0]?.total || 0,
              total_jogos: jogos[0]?.total || 0,
              valor_total_arrecadado: parseFloat(bolao.valor_aposta) * (bilhetes[0]?.total || 0)
            }
          }
        } catch (error) {
          console.error(`❌ Erro ao buscar stats do bolão ${bolao.id}:`, error)
          return {
            ...bolao,
            stats: {
              total_bilhetes: 0,
              total_apostas: 0,
              total_jogos: 0,
              valor_total_arrecadado: 0
            }
          }
        }
      })
    )

    return NextResponse.json({
      success: true,
      message: "Bolões listados com sucesso",
      boloes: boloesComStats,
      total: boloes.length,
      timestamp: new Date().toISOString()
    })

  } catch (error: any) {
    console.error("❌ Erro ao listar bolões:", error)
    
    return NextResponse.json(
      { 
        error: "Erro interno do servidor",
        message: "Não foi possível listar os bolões",
        details: error.message
      },
      { status: 500 }
    )
  }
}
