import { NextRequest, NextResponse } from 'next/server'
import { executeQuery, initializeDatabase } from '@/lib/database-config'

export async function GET(request: NextRequest) {
  try {
    await initializeDatabase()
    
    const { searchParams } = new URL(request.url)
    const codigo = searchParams.get('codigo')
    const transaction_id = searchParams.get('transaction_id')
    const valor = searchParams.get('valor')
    
    console.log("🔍 Debug bilhete - Parâmetros:", { codigo, transaction_id, valor })
    
    let bilhetes = []
    let query = ""
    let params = []
    
    if (codigo) {
      query = "SELECT * FROM bilhetes WHERE codigo LIKE ? ORDER BY created_at DESC LIMIT 5"
      params = [`%${codigo}%`]
    } else if (transaction_id) {
      query = "SELECT * FROM bilhetes WHERE transaction_id LIKE ? ORDER BY created_at DESC LIMIT 5"
      params = [`%${transaction_id}%`]
    } else if (valor) {
      query = "SELECT * FROM bilhetes WHERE valor_total = ? ORDER BY created_at DESC LIMIT 5"
      params = [parseFloat(valor)]
    } else {
      // Buscar bilhetes mais recentes
      query = "SELECT * FROM bilhetes ORDER BY created_at DESC LIMIT 10"
      params = []
    }
    
    bilhetes = await executeQuery(query, params)
    
    const bilhetesFormatados = bilhetes.map((b: any) => ({
      id: b.id,
      codigo: b.codigo,
      transaction_id: b.transaction_id,
      status: b.status,
      valor: parseFloat(b.valor_total),
      usuario_nome: b.usuario_nome,
      usuario_email: b.usuario_email,
      created_at: b.created_at,
      updated_at: b.updated_at
    }))
    
    return NextResponse.json({
      success: true,
      parametros_busca: { codigo, transaction_id, valor },
      bilhetes_encontrados: bilhetesFormatados.length,
      bilhetes: bilhetesFormatados,
      instrucoes: {
        buscar_por_codigo: "/api/debug-bilhete?codigo=BLT123",
        buscar_por_transaction_id: "/api/debug-bilhete?transaction_id=pix1_123",
        buscar_por_valor: "/api/debug-bilhete?valor=0.60",
        listar_recentes: "/api/debug-bilhete"
      }
    })
    
  } catch (error) {
    console.error("❌ Erro no debug de bilhete:", error)
    return NextResponse.json({
      error: "Erro interno do servidor",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    await initializeDatabase()
    
    const body = await request.json()
    const { codigo, transaction_id, novo_status = 'pago' } = body
    
    console.log("🔧 Atualizar bilhete manualmente:", { codigo, transaction_id, novo_status })
    
    if (!codigo && !transaction_id) {
      return NextResponse.json({ 
        error: "codigo ou transaction_id é obrigatório" 
      }, { status: 400 })
    }
    
    let query = ""
    let params = []
    
    if (codigo) {
      query = "UPDATE bilhetes SET status = ?, updated_at = NOW() WHERE codigo = ?"
      params = [novo_status, codigo]
    } else {
      query = "UPDATE bilhetes SET status = ?, updated_at = NOW() WHERE transaction_id = ?"
      params = [novo_status, transaction_id]
    }
    
    const updateResult = await executeQuery(query, params)
    
    // Buscar bilhete atualizado
    const bilheteQuery = codigo 
      ? "SELECT * FROM bilhetes WHERE codigo = ? LIMIT 1"
      : "SELECT * FROM bilhetes WHERE transaction_id = ? LIMIT 1"
    const bilheteParams = codigo ? [codigo] : [transaction_id]
    
    const bilhetes = await executeQuery(bilheteQuery, bilheteParams)
    
    if (bilhetes.length === 0) {
      return NextResponse.json({
        error: "Bilhete não encontrado após atualização",
        update_result: updateResult
      }, { status: 404 })
    }
    
    const bilhete = bilhetes[0]
    
    return NextResponse.json({
      success: true,
      message: "Bilhete atualizado com sucesso",
      bilhete: {
        id: bilhete.id,
        codigo: bilhete.codigo,
        transaction_id: bilhete.transaction_id,
        status: bilhete.status,
        valor: parseFloat(bilhete.valor_total),
        usuario_nome: bilhete.usuario_nome,
        updated_at: bilhete.updated_at
      },
      linhas_afetadas: (updateResult as any)?.affectedRows || 0
    })
    
  } catch (error) {
    console.error("❌ Erro ao atualizar bilhete:", error)
    return NextResponse.json({
      error: "Erro interno do servidor",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}
