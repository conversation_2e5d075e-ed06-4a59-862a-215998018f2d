import { NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

// Simula um pagamento PIX sendo detectado e processa automaticamente
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { bilhete_codigo, transaction_id, valor } = body

    console.log("💳 Simulando pagamento PIX detectado:", { bilhete_codigo, transaction_id, valor })

    await initializeDatabase()

    // Buscar o bilhete
    let bilhete
    if (bilhete_codigo) {
      const bilhetes = await executeQuery(`
        SELECT id, codigo, transaction_id, pix_order_id, status, valor_total, usuario_id
        FROM bilhetes 
        WHERE codigo = ? OR transaction_id = ?
        LIMIT 1
      `, [bilhete_codigo, bilhete_codigo])
      
      bilhete = bilhetes[0]
    } else if (transaction_id) {
      const bilhetes = await executeQuery(`
        SELECT id, codigo, transaction_id, pix_order_id, status, valor_total, usuario_id
        FROM bilhetes 
        WHERE transaction_id = ?
        LIMIT 1
      `, [transaction_id])
      
      bilhete = bilhetes[0]
    }

    if (!bilhete) {
      return NextResponse.json({
        success: false,
        message: "Bilhete não encontrado",
        bilhete_codigo,
        transaction_id
      }, { status: 404 })
    }

    console.log("🎫 Bilhete encontrado:", bilhete)

    // Se já está pago, não fazer nada
    if (bilhete.status === 'pago') {
      return NextResponse.json({
        success: true,
        message: "Bilhete já estava pago",
        bilhete: {
          codigo: bilhete.codigo,
          status: bilhete.status,
          valor: parseFloat(bilhete.valor_total)
        },
        action: "none"
      })
    }

    // Simular que o pagamento foi detectado na API PIX
    console.log("🔍 Simulando detecção de pagamento PIX...")

    // Chamar o smart webhook para processar o pagamento
    try {
      console.log("🚀 Chamando smart webhook para processar pagamento...")
      
      const webhookResponse = await fetch(`${request.nextUrl.origin}/api/smart-webhook`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          order_id: bilhete.codigo,
          status: 'PAID',
          type: 'PIXOUT',
          message: 'Payment detected via PIX simulation',
          transaction_id: bilhete.transaction_id,
          valor: parseFloat(bilhete.valor_total)
        })
      })

      if (webhookResponse.ok) {
        const webhookResult = await webhookResponse.json()
        console.log("✅ Smart webhook executado com sucesso!")

        // Verificar se o bilhete foi atualizado
        const bilheteAtualizado = await executeQuery(`
          SELECT id, codigo, status, valor_total, updated_at
          FROM bilhetes 
          WHERE id = ?
        `, [bilhete.id])

        return NextResponse.json({
          success: true,
          message: "Pagamento PIX simulado e processado com sucesso!",
          bilhete_original: {
            codigo: bilhete.codigo,
            status: bilhete.status,
            valor: parseFloat(bilhete.valor_total)
          },
          bilhete_atualizado: bilheteAtualizado[0] ? {
            codigo: bilheteAtualizado[0].codigo,
            status: bilheteAtualizado[0].status,
            valor: parseFloat(bilheteAtualizado[0].valor_total),
            updated_at: bilheteAtualizado[0].updated_at
          } : null,
          webhook_result: webhookResult,
          simulation_timestamp: new Date().toISOString()
        })

      } else {
        console.log("❌ Erro no smart webhook:", webhookResponse.status)
        return NextResponse.json({
          success: false,
          message: "Erro ao processar webhook",
          error: `HTTP ${webhookResponse.status}`,
          bilhete: {
            codigo: bilhete.codigo,
            status: bilhete.status
          }
        }, { status: 500 })
      }

    } catch (webhookError: any) {
      console.error("❌ Erro ao chamar smart webhook:", webhookError)
      return NextResponse.json({
        success: false,
        message: "Erro ao processar pagamento",
        error: webhookError.message,
        bilhete: {
          codigo: bilhete.codigo,
          status: bilhete.status
        }
      }, { status: 500 })
    }

  } catch (error: any) {
    console.error('❌ Erro na simulação de pagamento PIX:', error)
    return NextResponse.json({
      success: false,
      message: 'Erro na simulação',
      error: error.message
    }, { status: 500 })
  }
}

// Método GET para listar bilhetes pendentes que podem ser pagos
export async function GET(request: NextRequest) {
  try {
    await initializeDatabase()

    const bilhetesPendentes = await executeQuery(`
      SELECT 
        id,
        codigo,
        transaction_id,
        pix_order_id,
        valor_total,
        created_at,
        usuario_id
      FROM bilhetes 
      WHERE status = 'pendente' 
      AND created_at >= DATE_SUB(NOW(), INTERVAL 2 HOUR)
      ORDER BY created_at DESC
      LIMIT 10
    `)

    return NextResponse.json({
      message: "Simulador de Pagamento PIX",
      bilhetes_pendentes: bilhetesPendentes.length,
      bilhetes: bilhetesPendentes.map(b => ({
        codigo: b.codigo,
        transaction_id: b.transaction_id,
        valor: parseFloat(b.valor_total),
        created_at: b.created_at,
        simular_url: `POST /api/simulate-pix-payment`,
        simular_body: {
          bilhete_codigo: b.codigo,
          transaction_id: b.transaction_id,
          valor: parseFloat(b.valor_total)
        }
      })),
      instrucoes: [
        "1. Use POST /api/simulate-pix-payment para simular um pagamento",
        "2. Envie { bilhete_codigo: 'BLT...' } no body",
        "3. O sistema processará automaticamente via smart webhook",
        "4. Use GET /api/simulate-pix-payment para ver bilhetes pendentes"
      ]
    })

  } catch (error: any) {
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 })
  }
}
