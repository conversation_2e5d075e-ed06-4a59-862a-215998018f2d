import { NextRequest, NextResponse } from "next/server"

// Endpoint para testar o webhook automático com o bilhete específico
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { bilhete_codigo = "BLT175313245158527AS0R9Z", transaction_id = "pixi_01k0qdcbzmfmrs95pza2mdsrsn" } = body

    console.log("🧪 Testando webhook automático:", { bilhete_codigo, transaction_id })

    // Preparar payload do webhook baseado no bilhete que você mostrou
    const webhookPayload = {
      order_id: transaction_id,
      transaction_id: transaction_id,
      qr_code_payment_id: transaction_id,
      status: "PAID",
      type: "PIXOUT",
      message: "Pagamento confirmado - Teste Automático",
      amount: 0.13, // Valor que apareceu no seu teste
      description: `Bolão - ${bilhete_codigo}`,
      end_to_end_id: `E${Date.now()}`,
      last_updated_at: new Date().toISOString(),
      source: "test_auto_webhook",
      bilhete_info: {
        codigo: bilhete_codigo,
        valor: "R$ 0,13",
        data: "21/07/2025",
        hora: "18:18:37"
      }
    }

    console.log("📤 Enviando webhook de teste:", webhookPayload)

    // Chamar o webhook interno
    const webhookUrl = `${request.nextUrl.origin}/api/v1/MP/webhookruntransation`
    
    try {
      const webhookResponse = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Sistema-Bolao-Test-Auto-Webhook/1.0',
          'X-Test-Mode': 'true'
        },
        body: JSON.stringify(webhookPayload)
      })

      const webhookResult = await webhookResponse.json()
      
      console.log("✅ Webhook de teste executado:", webhookResult)

      // Também testar o trigger direto
      const triggerPayload = {
        transaction_id: transaction_id,
        bilhete_codigo: bilhete_codigo,
        force_trigger: true
      }

      const triggerResponse = await fetch(`${request.nextUrl.origin}/api/webhook-trigger`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(triggerPayload)
      })

      const triggerResult = await triggerResponse.json()

      return NextResponse.json({
        success: true,
        message: "Teste de webhook automático executado",
        timestamp: new Date().toISOString(),
        bilhete_testado: {
          codigo: bilhete_codigo,
          transaction_id: transaction_id,
          valor: "R$ 0,13"
        },
        webhook_direto: {
          url: webhookUrl,
          payload: webhookPayload,
          response: webhookResult,
          status: webhookResponse.status
        },
        webhook_trigger: {
          url: `${request.nextUrl.origin}/api/webhook-trigger`,
          payload: triggerPayload,
          response: triggerResult,
          status: triggerResponse.status
        },
        instrucoes: [
          "✅ Webhook direto testado",
          "✅ Webhook trigger testado", 
          "🔄 Verifique se o status do bilhete foi atualizado",
          "🎉 Modal de sucesso deve aparecer automaticamente"
        ]
      })

    } catch (webhookError) {
      console.error("❌ Erro ao testar webhook:", webhookError)
      
      return NextResponse.json({
        error: "Erro ao testar webhook",
        details: webhookError instanceof Error ? webhookError.message : String(webhookError),
        bilhete: {
          codigo: bilhete_codigo,
          transaction_id: transaction_id
        }
      }, { status: 500 })
    }

  } catch (error) {
    console.error("❌ Erro geral no teste de webhook:", error)
    
    return NextResponse.json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}

// Método GET para instruções de teste
export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: "Teste de Webhook Automático - Sistema Bolão",
    bilhete_exemplo: {
      codigo: "BLT175313245158527AS0R9Z",
      transaction_id: "pixi_01k0qdcbzmfmrs95pza2mdsrsn",
      valor: "R$ 0,13",
      status_atual: "pago"
    },
    como_testar: {
      metodo: "POST",
      url: "/api/test-webhook-auto",
      payload_opcional: {
        bilhete_codigo: "BLT175313245158527AS0R9Z",
        transaction_id: "pixi_01k0qdcbzmfmrs95pza2mdsrsn"
      }
    },
    outros_endpoints: {
      webhook_principal: "POST /api/v1/MP/webhookruntransation",
      webhook_trigger: "POST /api/webhook-trigger", 
      monitoramento_auto: "POST /api/auto-payment-monitor",
      configuracao_webhook: "GET /api/webhook-config"
    },
    fluxo_automatico: [
      "1. Cliente paga PIX via QR Code ou Copia e Cola",
      "2. API meiodepagamento.com detecta o pagamento",
      "3. API envia webhook para: https://ouroemu.site/api/v1/MP/webhookruntransation",
      "4. Sistema atualiza status do bilhete para 'pago'",
      "5. Frontend detecta mudança e exibe modal de sucesso",
      "6. 🎉 Evento disparado automaticamente!"
    ],
    configuracao_necessaria: {
      webhook_url: "https://ouroemu.site/api/v1/MP/webhookruntransation",
      configurar_em: "Painel da API meiodepagamento.com",
      formato_payload: {
        order_id: "string",
        transaction_id: "string", 
        status: "PAID|PENDING|CANCELLED",
        type: "PIXOUT",
        amount: "number"
      }
    }
  })
}
