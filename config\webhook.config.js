/**
 * CONFIGURAÇÃO DE WEBHOOKS - Sistema <PERSON>l<PERSON>
 * 
 * 🔗 URLs dos Webhooks PIX
 * 
 * ⚠️ IMPORTANTE: Altere as URLs abaixo conforme seu domínio
 */

// URLs base do sistema
const BASE_URLS = {
  development: 'http://localhost:3000',
  production: 'https://joanadeoxum.com', // ✅ CONFIGURADO
  staging: 'https://staging.joanadeoxum.com'
}

// Obter URL base baseada no ambiente
function getBaseUrl() {
  const env = process.env.NODE_ENV || 'development'
  return process.env.NEXT_PUBLIC_APP_URL || BASE_URLS[env] || BASE_URLS.development
}

// Configuração dos webhooks
export const WEBHOOK_CONFIG = {
  // URLs dos webhooks PIX
  pix: {
    // Webhook principal do Mercado Pago/Meio de Pagamento
    main: `${getBaseUrl()}/api/v1/MP/webhookruntransation`,
    
    // Webhook alternativo PIX
    alternative: `${getBaseUrl()}/api/webhook/pix`,
    
    // Webhook genérico PIX
    generic: `${getBaseUrl()}/api/pix/webhook`,
    
    // Webhook de status
    status: `${getBaseUrl()}/api/pix/status`
  },
  
  // Configurações de segurança
  security: {
    secret: process.env.PIX_WEBHOOK_SECRET || 'your_webhook_secret_here',
    timeout: 30000, // 30 segundos
    retries: 3
  },
  
  // Headers padrão
  headers: {
    'Content-Type': 'application/json',
    'User-Agent': 'Sistema-Bolao-Webhook/1.0'
  }
}

// Função para obter URL do webhook principal
export function getMainWebhookUrl() {
  return process.env.PIX_WEBHOOK_URL || WEBHOOK_CONFIG.pix.main
}

// Função para obter todas as URLs de webhook
export function getAllWebhookUrls() {
  return {
    main: getMainWebhookUrl(),
    alternative: WEBHOOK_CONFIG.pix.alternative,
    generic: WEBHOOK_CONFIG.pix.generic,
    status: WEBHOOK_CONFIG.pix.status
  }
}

// Função para validar URL do webhook
export function validateWebhookUrl(url) {
  try {
    const parsedUrl = new URL(url)
    return {
      valid: true,
      protocol: parsedUrl.protocol,
      host: parsedUrl.host,
      pathname: parsedUrl.pathname
    }
  } catch (error) {
    return {
      valid: false,
      error: error.message
    }
  }
}

// Função para log de configuração
export function logWebhookConfig() {
  const urls = getAllWebhookUrls()
  
  console.log('🔗 CONFIGURAÇÃO DE WEBHOOKS:')
  console.log('📍 URL Principal:', urls.main)
  console.log('📍 URL Alternativa:', urls.alternative)
  console.log('📍 URL Genérica:', urls.generic)
  console.log('📍 URL Status:', urls.status)
  console.log('🔒 Secret configurado:', !!WEBHOOK_CONFIG.security.secret)
  console.log('⏱️ Timeout:', WEBHOOK_CONFIG.security.timeout + 'ms')
}

// Instruções para configuração
export const WEBHOOK_INSTRUCTIONS = {
  title: '🔧 COMO CONFIGURAR O WEBHOOK',
  steps: [
    '1. Altere "seudominio.com" no arquivo .env.local',
    '2. Configure PIX_WEBHOOK_URL com sua URL real',
    '3. Configure o webhook na API meiodepagamento.com',
    '4. Teste com /api/test-webhook',
    '5. Monitore logs com /api/debug-bilhete'
  ],
  urls_to_configure: [
    'PIX_WEBHOOK_URL=https://seudominio.com/api/v1/MP/webhookruntransation',
    'NEXT_PUBLIC_APP_URL=https://seudominio.com'
  ],
  api_endpoints: [
    'POST /api/v1/MP/webhookruntransation (Principal)',
    'POST /api/webhook/pix (Alternativo)',
    'POST /api/pix/webhook (Genérico)',
    'GET /api/test-webhook (Teste)',
    'GET /api/debug-bilhete (Debug)'
  ]
}

// Exportar configurações para uso no Node.js
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    WEBHOOK_CONFIG,
    getMainWebhookUrl,
    getAllWebhookUrls,
    validateWebhookUrl,
    logWebhookConfig,
    WEBHOOK_INSTRUCTIONS
  }
}
