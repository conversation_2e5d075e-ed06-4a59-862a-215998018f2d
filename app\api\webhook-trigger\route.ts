import { NextRequest, NextResponse } from "next/server"
import { executeQuery } from "@/lib/database-config"

// Endpoint para disparar webhook automaticamente quando pagamento for detectado
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { transaction_id, bilhete_codigo, force_trigger = false } = body

    console.log("🚀 Disparando webhook automático:", { 
      transaction_id, 
      bilhete_codigo, 
      force_trigger 
    })

    if (!transaction_id && !bilhete_codigo) {
      return NextResponse.json({
        error: "transaction_id ou bilhete_codigo é obrigatório"
      }, { status: 400 })
    }

    // Buscar bilhete no banco
    const bilhete = await executeQuery(`
      SELECT * FROM bilhetes
      WHERE transaction_id = ? OR codigo = ?
      LIMIT 1
    `, [transaction_id || bilhete_codigo, bilhete_codigo || transaction_id])

    if (!Array.isArray(bilhete) || bilhete.length === 0) {
      return NextResponse.json({
        error: "Bilhete não encontrado",
        transaction_id,
        bilhete_codigo
      }, { status: 404 })
    }

    const bilheteData = bilhete[0] as any
    console.log("📋 Bilhete encontrado:", {
      id: bilheteData.id,
      codigo: bilheteData.codigo,
      status: bilheteData.status,
      transaction_id: bilheteData.transaction_id
    })

    // Verificar se já está pago (a menos que force_trigger seja true)
    if (bilheteData.status === 'pago' && !force_trigger) {
      return NextResponse.json({
        message: "Bilhete já está pago",
        bilhete: {
          codigo: bilheteData.codigo,
          status: bilheteData.status,
          transaction_id: bilheteData.transaction_id
        },
        webhook_needed: false
      })
    }

    // Preparar payload do webhook
    const webhookPayload = {
      order_id: bilheteData.transaction_id || bilheteData.codigo,
      transaction_id: bilheteData.transaction_id,
      qr_code_payment_id: bilheteData.transaction_id,
      status: "PAID",
      type: "PIXOUT",
      message: "Pagamento confirmado automaticamente",
      amount: parseFloat(bilheteData.valor_total),
      description: `Bolão - ${bilheteData.codigo}`,
      end_to_end_id: `E${Date.now()}`,
      last_updated_at: new Date().toISOString(),
      source: "auto_trigger"
    }

    console.log("📤 Enviando webhook payload:", webhookPayload)

    // Chamar o webhook interno
    const webhookUrl = `${request.nextUrl.origin}/api/v1/MP/webhookruntransation`
    
    try {
      const webhookResponse = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Sistema-Bolao-Auto-Webhook/1.0'
        },
        body: JSON.stringify(webhookPayload)
      })

      const webhookResult = await webhookResponse.json()
      
      console.log("✅ Webhook disparado com sucesso:", webhookResult)

      return NextResponse.json({
        success: true,
        message: "Webhook disparado automaticamente",
        bilhete: {
          codigo: bilheteData.codigo,
          status_anterior: bilheteData.status,
          transaction_id: bilheteData.transaction_id
        },
        webhook: {
          url: webhookUrl,
          payload: webhookPayload,
          response: webhookResult,
          status: webhookResponse.status
        },
        triggered_at: new Date().toISOString()
      })

    } catch (webhookError) {
      console.error("❌ Erro ao disparar webhook:", webhookError)
      
      return NextResponse.json({
        error: "Erro ao disparar webhook",
        details: webhookError instanceof Error ? webhookError.message : String(webhookError),
        bilhete: {
          codigo: bilheteData.codigo,
          transaction_id: bilheteData.transaction_id
        }
      }, { status: 500 })
    }

  } catch (error) {
    console.error("❌ Erro geral no trigger de webhook:", error)
    
    return NextResponse.json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}

// Método GET para listar bilhetes que podem ter webhook disparado
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status') || 'pendente'
    const limit = parseInt(searchParams.get('limit') || '10')

    // Buscar bilhetes que podem ter webhook disparado
    const bilhetes = await executeQuery(`
      SELECT 
        id,
        codigo,
        status,
        transaction_id,
        valor_total,
        created_at,
        updated_at
      FROM bilhetes
      WHERE status = ?
      AND created_at >= DATE_SUB(NOW(), INTERVAL 2 HOUR)
      ORDER BY created_at DESC
      LIMIT ?
    `, [status, limit])

    return NextResponse.json({
      message: "Bilhetes disponíveis para trigger de webhook",
      status_filter: status,
      count: bilhetes.length,
      bilhetes: bilhetes.map((b: any) => ({
        codigo: b.codigo,
        status: b.status,
        transaction_id: b.transaction_id,
        valor: parseFloat(b.valor_total),
        created_at: b.created_at,
        trigger_url: `${request.nextUrl.origin}/api/webhook-trigger`,
        trigger_payload: {
          transaction_id: b.transaction_id,
          bilhete_codigo: b.codigo
        }
      })),
      usage: {
        trigger_webhook: "POST /api/webhook-trigger",
        payload: {
          transaction_id: "string",
          bilhete_codigo: "string (opcional)",
          force_trigger: "boolean (opcional)"
        }
      }
    })

  } catch (error) {
    return NextResponse.json({
      error: "Erro ao buscar bilhetes",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}
