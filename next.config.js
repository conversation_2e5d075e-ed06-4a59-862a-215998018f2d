/** @type {import('next').NextConfig} */
const nextConfig = {
  // Pular verificação de tipos durante o build (temporário)
  typescript: {
    ignoreBuildErrors: true,
  },

  // Configurações de build
  experimental: {
    // Configurações experimentais do Next.js
  },

  // Configurações de produção
  poweredByHeader: false,
  compress: true,

  // Configurar variáveis de ambiente para build
  env: {
    NEXT_PHASE: process.env.NEXT_PHASE || 'phase-development-server',
  },

  // Configurar rotas que devem ser ignoradas durante o build
  generateBuildId: async () => {
    // Definir variável de ambiente para indicar que estamos em build
    process.env.NEXT_BUILD_MODE = 'true'
    return 'build-' + Date.now()
  },
  
  // Configurações de webpack
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // Configurações personalizadas do webpack se necessário
    return config
  },
  
  // Configurações de headers
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-store, no-cache, must-revalidate, proxy-revalidate',
          },
        ],
      },
    ]
  },
  
  // Configurações de redirects
  async redirects() {
    return []
  },
  
  // Configurações de rewrites
  async rewrites() {
    return []
  },
}

export default nextConfig
